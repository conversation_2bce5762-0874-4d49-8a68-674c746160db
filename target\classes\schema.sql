-- 汽车维修预约服务系统数据库初始化脚本

-- 字典表
CREATE TABLE IF NOT EXISTS dictionary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dic_code VARCHAR(100) COMMENT '字段',
    dic_name VARCHAR(100) COMMENT '字段名',
    code_index INT COMMENT '编码',
    index_name VARCHAR(100) COMMENT '编码名字',
    super_id INT COMMENT '父字段id',
    beizhu VARCHAR(255) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 用户表
CREATE TABLE IF NOT EXISTS yonghu (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) COMMENT '用户名',
    password VARCHAR(100) COMMENT '密码',
    yonghu_name VARCHAR(100) COMMENT '用户姓名',
    yonghu_photo VARCHAR(255) COMMENT '头像',
    yonghu_phone VARCHAR(100) COMMENT '联系方式',
    yonghu_email VARCHAR(100) COMMENT '邮箱',
    sex_types INT COMMENT '性别',
    new_money DECIMAL(10,2) COMMENT '余额',
    yonghu_delete INT DEFAULT 1 COMMENT '假删',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 管理员表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) COMMENT '用户名',
    password VARCHAR(100) COMMENT '密码',
    role VARCHAR(100) COMMENT '角色',
    addtime TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间'
);

-- 商品表
CREATE TABLE IF NOT EXISTS goods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    goods_name VARCHAR(200) COMMENT '商品名称',
    goods_types INT COMMENT '商品类型',
    goods_photo VARCHAR(255) COMMENT '商品照片',
    goods_clicknum INT DEFAULT 0 COMMENT '点击次数',
    zan_number INT DEFAULT 0 COMMENT '赞',
    cai_number INT DEFAULT 0 COMMENT '踩',
    shangxia_types INT COMMENT '是否上架',
    goods_new_money DECIMAL(10,2) COMMENT '现价',
    insert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间',
    goods_content TEXT COMMENT '商品详情',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 商品收藏表
CREATE TABLE IF NOT EXISTS goods_collection (
    id INT AUTO_INCREMENT PRIMARY KEY,
    goods_id INT COMMENT '商品',
    yonghu_id INT COMMENT '用户',
    goods_collection_types INT COMMENT '类型',
    insert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 商品留言表
CREATE TABLE IF NOT EXISTS goods_liuyan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    goods_id INT COMMENT '商品',
    yonghu_id INT COMMENT '用户',
    goods_liuyan_text TEXT COMMENT '留言内容',
    reply_text TEXT COMMENT '回复内容',
    insert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '留言时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '回复时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 商品订单表
CREATE TABLE IF NOT EXISTS goods_order (
    id INT AUTO_INCREMENT PRIMARY KEY,
    goods_order_uuid_number VARCHAR(200) COMMENT '订单号',
    goods_id INT COMMENT '商品',
    yonghu_id INT COMMENT '用户',
    buy_number INT COMMENT '购买数量',
    goods_order_true_price DECIMAL(10,2) COMMENT '实付价格',
    goods_order_types INT COMMENT '订单类型',
    goods_order_payment_types INT COMMENT '支付类型',
    insert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 车辆表
CREATE TABLE IF NOT EXISTS cheliang (
    id INT AUTO_INCREMENT PRIMARY KEY,
    yonghu_id INT COMMENT '用户',
    cheliang_name VARCHAR(200) COMMENT '车辆名称',
    cheliang_types INT COMMENT '车辆类型',
    cheliang_photo VARCHAR(255) COMMENT '车辆照片',
    cheliang_chepai VARCHAR(200) COMMENT '车牌号',
    cheliang_content TEXT COMMENT '车辆介绍',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 门店表
CREATE TABLE IF NOT EXISTS mendian (
    id INT AUTO_INCREMENT PRIMARY KEY,
    mendian_name VARCHAR(200) COMMENT '门店名称',
    mendian_types INT COMMENT '门店类型',
    mendian_photo VARCHAR(255) COMMENT '门店照片',
    mendian_address VARCHAR(200) COMMENT '门店位置',
    mendian_phone VARCHAR(200) COMMENT '联系方式',
    mendian_content TEXT COMMENT '门店介绍',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 论坛表
CREATE TABLE IF NOT EXISTS forum (
    id INT AUTO_INCREMENT PRIMARY KEY,
    forum_name VARCHAR(200) COMMENT '帖子标题',
    yonghu_id INT COMMENT '用户',
    users_id INT COMMENT '管理员',
    forum_content TEXT COMMENT '发布内容',
    super_ids VARCHAR(1000) COMMENT '父id',
    forum_state_types INT COMMENT '帖子状态',
    insert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发帖时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 公告信息表
CREATE TABLE IF NOT EXISTS news (
    id INT AUTO_INCREMENT PRIMARY KEY,
    news_name VARCHAR(200) COMMENT '公告标题',
    news_types INT COMMENT '公告类型',
    news_photo VARCHAR(255) COMMENT '公告图片',
    insert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '公告时间',
    news_content TEXT COMMENT '公告详情',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 配置文件表
CREATE TABLE IF NOT EXISTS config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '配置参数名称',
    value VARCHAR(255) COMMENT '配置参数值'
);

-- token表
CREATE TABLE IF NOT EXISTS token (
    id INT AUTO_INCREMENT PRIMARY KEY,
    userid INT NOT NULL COMMENT '用户id',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    tablename VARCHAR(100) COMMENT '表名',
    role VARCHAR(100) COMMENT '角色',
    token VARCHAR(200) NOT NULL COMMENT 'token',
    addtime TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
    expiratedtime TIMESTAMP COMMENT '过期时间'
);

-- 插入基础字典数据
INSERT INTO dictionary (dic_code, dic_name, code_index, index_name, super_id, beizhu) VALUES
('sex_types', '性别', 1, '男', NULL, NULL),
('sex_types', '性别', 2, '女', NULL, NULL),
('goods_types', '商品类型', 1, '维修服务', NULL, NULL),
('goods_types', '商品类型', 2, '保养服务', NULL, NULL),
('goods_types', '商品类型', 3, '配件销售', NULL, NULL),
('shangxia_types', '上架状态', 1, '上架', NULL, NULL),
('shangxia_types', '上架状态', 2, '下架', NULL, NULL),
('goods_collection_types', '收藏类型', 1, '收藏', NULL, NULL),
('goods_order_types', '订单类型', 1, '已支付', NULL, NULL),
('goods_order_types', '订单类型', 2, '退款', NULL, NULL),
('goods_order_types', '订单类型', 3, '已发货', NULL, NULL),
('goods_order_types', '订单类型', 4, '已收货', NULL, NULL),
('goods_order_types', '订单类型', 5, '已评价', NULL, NULL),
('goods_order_payment_types', '支付类型', 1, '现金', NULL, NULL),
('goods_order_payment_types', '支付类型', 2, '积分', NULL, NULL),
('cheliang_types', '车辆类型', 1, '轿车', NULL, NULL),
('cheliang_types', '车辆类型', 2, 'SUV', NULL, NULL),
('cheliang_types', '车辆类型', 3, '货车', NULL, NULL),
('mendian_types', '门店类型', 1, '维修门店', NULL, NULL),
('mendian_types', '门店类型', 2, '保养门店', NULL, NULL),
('forum_state_types', '帖子状态', 1, '发帖', NULL, NULL),
('forum_state_types', '帖子状态', 2, '回帖', NULL, NULL),
('news_types', '公告类型', 1, '公告信息', NULL, NULL),
('news_types', '公告类型', 2, '系统通知', NULL, NULL);

-- 插入默认管理员用户
INSERT INTO users (username, password, role) VALUES ('admin', '123456', '管理员');

-- 插入测试用户
INSERT INTO yonghu (username, password, yonghu_name, yonghu_phone, yonghu_email, sex_types, new_money, yonghu_delete) VALUES 
('user1', '123456', '张三', '13800138001', '<EMAIL>', 1, 1000.00, 1),
('user2', '123456', '李四', '13800138002', '<EMAIL>', 2, 500.00, 1);
