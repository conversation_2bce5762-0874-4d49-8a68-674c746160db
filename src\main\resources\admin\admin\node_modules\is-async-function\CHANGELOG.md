# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.1.1](https://github.com/inspect-js/is-async-function/compare/v2.1.0...v2.1.1) - 2025-01-22

### Fixed

- [Refactor] use `async-function` for the eval parts [`#31`](https://github.com/inspect-js/is-async-function/issues/31)

### Commits

- [Refactor] move `new Function` helper into a separate file [`db36da5`](https://github.com/inspect-js/is-async-function/commit/db36da5467fbbf0f2ae264114be6aa9edf55e218)
- [Dev Deps] update `@arethetypeswrong/cli`, `@types/tape` [`981ab90`](https://github.com/inspect-js/is-async-function/commit/981ab907b700d344b510ca1617fda00a66513aa2)
- [meta] add `exports` [`81bb8e5`](https://github.com/inspect-js/is-async-function/commit/81bb8e578c8cbeeda742715ab7a935c0472866a1)
- [Refactor] skip `getProto` call when `AsyncFunction` does not exist [`dc929a5`](https://github.com/inspect-js/is-async-function/commit/dc929a547a5f7cd1337b176d20135b8cc3cc23cb)

## [v2.1.0](https://github.com/inspect-js/is-async-function/compare/v2.0.0...v2.1.0) - 2025-01-02

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`b8d050b`](https://github.com/inspect-js/is-async-function/commit/b8d050ba1ab615ef748e8ffbe48c5fc4e14af510)
- [actions] split out node 10-20, and 20+ [`1c8cd4b`](https://github.com/inspect-js/is-async-function/commit/1c8cd4bb565934ec5a4d9fc351ac9c8e2b217c07)
- [New] add types [`5ba6244`](https://github.com/inspect-js/is-async-function/commit/5ba62441efb4eb267659bcccd615018e7e09cc30)
- [Robustness] use `call-bound`, `safe-regex-test` [`9379ecd`](https://github.com/inspect-js/is-async-function/commit/9379ecda6ddce8460a6a0dc1a3d5555443b0b510)
- [actions] update rebase action to use reusable workflow [`81b54fb`](https://github.com/inspect-js/is-async-function/commit/81b54fbcb881ac0000306dfd8aade39c51191256)
- [Tests] use `for-each` [`ebdc486`](https://github.com/inspect-js/is-async-function/commit/ebdc486ab08c35651567c9f21f11a18ab5618737)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `tape` [`9eb494f`](https://github.com/inspect-js/is-async-function/commit/9eb494f5f5a89eebf7981db906c0c821598465fe)
- [Dev Deps] update `aud`, `tape` [`ea43809`](https://github.com/inspect-js/is-async-function/commit/ea43809bff25e47e196bde254c7383464c03ae7e)
- [Refactor] use `get-proto` directly [`fc46390`](https://github.com/inspect-js/is-async-function/commit/fc4639088bb6ed4e8b19988d3e642d45ee45e70e)
- [Tests] replace `aud` with `npm audit` [`edb4afb`](https://github.com/inspect-js/is-async-function/commit/edb4afb68d6d63d049608269fdf83cdb98956ff3)
- [Deps] update `has-tostringtag` [`dc78cf5`](https://github.com/inspect-js/is-async-function/commit/dc78cf545f87ecc17fc7f3426bbbc5ecdea9d167)
- [Dev Deps] add missing peer dep [`a93d8ff`](https://github.com/inspect-js/is-async-function/commit/a93d8ff0a22e4351d625ca5bb0fc73d222f9756c)

## [v2.0.0](https://github.com/inspect-js/is-async-function/compare/v1.3.0...v2.0.0) - 2022-04-11

### Commits

- [Breaking] v2 implementation and tests [`d79a37e`](https://github.com/inspect-js/is-async-function/commit/d79a37e25e24a74be3c349de51fda4ad58f30f3a)
- Initial commit [`456defc`](https://github.com/inspect-js/is-async-function/commit/456defc6dc36809d11dd5a199110e46fe9fb4a6f)
- npm init [`d35b611`](https://github.com/inspect-js/is-async-function/commit/d35b611d669e57a3a6fd017930d3bf9a0589ffd2)
- Only apps should have lockfiles [`5920874`](https://github.com/inspect-js/is-async-function/commit/5920874ec2b26762d1037168a73e1c29a4286b1a)

## [v1.3.0](https://github.com/inspect-js/is-async-function/compare/v1.2.4...v1.3.0) - 2020-01-15

### Commits

- feat: update deps, ci, readme [`dbb52a7`](https://github.com/inspect-js/is-async-function/commit/dbb52a7714887e897df62302a6ad8e8402d67fe4)
- chore(release): 1.3.0 [`972e26c`](https://github.com/inspect-js/is-async-function/commit/972e26c01ad14aa3560462624f24f4464efc46a3)

## [v1.2.4](https://github.com/inspect-js/is-async-function/compare/v1.2.3...v1.2.4) - 2020-01-15

### Merged

- fix(src): remove unneeded condition [`#16`](https://github.com/inspect-js/is-async-function/pull/16)

### Fixed

- fix: update deps + the tests; close #17 [`#17`](https://github.com/inspect-js/is-async-function/issues/17)

### Commits

- chore: update broken badge links [`2985e36`](https://github.com/inspect-js/is-async-function/commit/2985e36a644306ab2beb392e6833860cc1b3dadf)
- chore: add github funding file [`377233a`](https://github.com/inspect-js/is-async-function/commit/377233ab85f042e68b4cf5c7fd91860b77e8ce53)
- chore: all modules are stable for years [`b7db9f0`](https://github.com/inspect-js/is-async-function/commit/b7db9f07cfcd8e3f7f7c448f71dcf73ef0e9b67e)
- chore(release): 1.2.4 [`a9f441f`](https://github.com/inspect-js/is-async-function/commit/a9f441f0b801dabf6e8a1690c871fd564e56ddad)
- fix: add npm funding field [`c05ef28`](https://github.com/inspect-js/is-async-function/commit/c05ef28a1aa33543fc29c1c897613dc8676c1afc)
- chore: drop testing on old Node versions [`c975f68`](https://github.com/inspect-js/is-async-function/commit/c975f68a788d99ebf109a34ed9867f19c85b8805)
- chore(ci): test on 6 only [`106dcdd`](https://github.com/inspect-js/is-async-function/commit/106dcdd6d225248ef55c41218706fef1a7ce8c0e)

## [v1.2.3](https://github.com/inspect-js/is-async-function/compare/v1.2.2...v1.2.3) - 2017-03-11

### Commits

- fix(style): remove lazy-cache, and update boilerplate stuff [`abd32db`](https://github.com/inspect-js/is-async-function/commit/abd32dba0d727e9a75fffa7ef7df138bbc722b69)
- fix(docs): regenerate readme [`592f1b7`](https://github.com/inspect-js/is-async-function/commit/592f1b721c7c81bc1a6a84e462f4bd7ba7f24cd3)
- fix(package): add missing dependency [`73f404d`](https://github.com/inspect-js/is-async-function/commit/73f404d9afc054c90c1f1bee280497ac809b1eb3)
- chore(release): 1.2.3 [`103cf28`](https://github.com/inspect-js/is-async-function/commit/103cf28cfa5302a5a00bb5c9bd8bf9ecc69999fa)
- fix(package): wrong version, because too fast update ;d [`2e887f0`](https://github.com/inspect-js/is-async-function/commit/2e887f09dbc9e234f5f26cadd4e7fe9cc97184fb)

## [v1.2.2](https://github.com/inspect-js/is-async-function/compare/v1.2.1...v1.2.2) - 2016-10-29

### Fixed

- fix(docs): fixes and updates API docs [`#14`](https://github.com/inspect-js/is-async-function/issues/14)

### Commits

- chore(release): 1.2.2 [`9165f94`](https://github.com/inspect-js/is-async-function/commit/9165f942865906a02e0f9afe55ac2f305d71a9b1)

## [v1.2.1](https://github.com/inspect-js/is-async-function/compare/v1.2.0...v1.2.1) - 2016-10-27

### Fixed

- fix(non-strict): fix a bug in non-strict mode [`#13`](https://github.com/inspect-js/is-async-function/issues/13)

### Commits

- chore(release): 1.2.1 [`f4c7f02`](https://github.com/inspect-js/is-async-function/commit/f4c7f02ecb1ca02772890552797da3b39883ed43)

## [v1.2.0](https://github.com/inspect-js/is-async-function/compare/v1.1.5...v1.2.0) - 2016-10-27

### Merged

- Revert "Update arr-includes to version 2.0.0 🚀" [`#12`](https://github.com/inspect-js/is-async-function/pull/12)
- chore(package): update arr-includes to version 2.0.0 [`#11`](https://github.com/inspect-js/is-async-function/pull/11)

### Commits

- feat(strict): introduce strict mode [`ef8526f`](https://github.com/inspect-js/is-async-function/commit/ef8526ffa8ba2b4cf37f6bd2dae21aee871e6e6a)
- docs(update): api docs [`739eb54`](https://github.com/inspect-js/is-async-function/commit/739eb5482ba560ad2de153a29fc16778f4d3ef3f)
- chore(release): 1.2.0 [`3222afe`](https://github.com/inspect-js/is-async-function/commit/3222afed47c9b5d2fa12490aff2d7c9887183ea2)

## [v1.1.5](https://github.com/inspect-js/is-async-function/compare/v1.1.4...v1.1.5) - 2016-09-21

### Commits

- chore(tests): simplify tests [`be112bb`](https://github.com/inspect-js/is-async-function/commit/be112bb3dec204f174bf745056b7c0fc7377aef5)
- Release v1.1.5 [`577d96c`](https://github.com/inspect-js/is-async-function/commit/577d96c9f0fb6288e6bc88d8e51703b98f19b20c)

## [v1.1.4](https://github.com/inspect-js/is-async-function/compare/v1.1.3...v1.1.4) - 2016-09-21

### Fixed

- chore(package): update deps, use lazy-cache - closes #10 [`#10`](https://github.com/inspect-js/is-async-function/issues/10)

### Commits

- chore(package/tests): update to use `mukla` instead of `assertit` lib [`83011b1`](https://github.com/inspect-js/is-async-function/commit/83011b1e2820e239c606d872468582f5a9249c47)
- chore(package): update npm scripts, add coveralls/standard/nyc to devDeps [`631acbd`](https://github.com/inspect-js/is-async-function/commit/631acbdcf9fd9aa6d56ac98e10f3092e7e5be485)
- chore(gitignore): update gitignore [`7f09f8f`](https://github.com/inspect-js/is-async-function/commit/7f09f8f40777879e70315dfeef0805755a44a293)
- chore(editorconfig): update editorconfig [`8bb8593`](https://github.com/inspect-js/is-async-function/commit/8bb85939f87e6738c5ca35b5e5f5d8cca67353c9)
- docs(readme): run verb to update readme [`cf15044`](https://github.com/inspect-js/is-async-function/commit/cf150446dc4e948f2629377d7f369824bfda8b3b)
- Release v1.1.4 [`41190e1`](https://github.com/inspect-js/is-async-function/commit/41190e1da3aeb787921b3ea5d834634295c31de8)

## [v1.1.3](https://github.com/inspect-js/is-async-function/compare/v1.1.2...v1.1.3) - 2016-04-21

### Merged

- chore(package): update is-match to version 0.4.1 [`#7`](https://github.com/inspect-js/is-async-function/pull/7)

### Commits

- use `common-callback-names` [`37c253f`](https://github.com/inspect-js/is-async-function/commit/37c253f9a34b68acd5651075a3f1b74cd30ed8fd)
- Release v1.1.3 [`f212193`](https://github.com/inspect-js/is-async-function/commit/f212193a00790ccae387e2d7a373076fcbbb9d8f)

## [v1.1.2](https://github.com/inspect-js/is-async-function/compare/v1.1.1...v1.1.2) - 2016-03-18

### Commits

- cleanup and update metadata [`e09ab8b`](https://github.com/inspect-js/is-async-function/commit/e09ab8b98e6aecd28d38ba4ff4c1f17c26549a06)
- update docs [`42920c6`](https://github.com/inspect-js/is-async-function/commit/42920c699f706fcf1048e0c039b335d7bbaf34ed)
- Release v1.1.2 [`19d77d6`](https://github.com/inspect-js/is-async-function/commit/19d77d6ba7abe87ecf4d2765cd8536fca140b3bd)

## [v1.1.1](https://github.com/inspect-js/is-async-function/compare/v1.1.0...v1.1.1) - 2016-03-18

### Commits

- run update [`27b21bf`](https://github.com/inspect-js/is-async-function/commit/27b21bf76254635d6c5c18f896b6f151938ae810)
- add docs [`ab38f94`](https://github.com/inspect-js/is-async-function/commit/ab38f947707182e0ea165e3bee90bd46b8dbfaf9)
- update docs [`90654c9`](https://github.com/inspect-js/is-async-function/commit/90654c93e8ff9d0cfd1443ae3609cd898b57ef11)
- refactor, allow passing custom array of argument names [`a1787c7`](https://github.com/inspect-js/is-async-function/commit/a1787c757f522cb5e1c568ec5270be14a38c4cc9)
- add related libs [`868f423`](https://github.com/inspect-js/is-async-function/commit/868f4235a449d610a87351e5a5070f42a0c6e7ce)
- Release v1.1.1 [`bc7d85e`](https://github.com/inspect-js/is-async-function/commit/bc7d85e2a115163d3b09f78be5196f21adce1a7c)
- update description [`ce2e97b`](https://github.com/inspect-js/is-async-function/commit/ce2e97b34762f2087699a5b9910e498ec7062090)

## [v1.1.0](https://github.com/inspect-js/is-async-function/compare/v1.0.0...v1.1.0) - 2015-06-25

### Commits

- Release v1.1.0 [`b3f3704`](https://github.com/inspect-js/is-async-function/commit/b3f3704f13a32664a08b3d55162925e37626f5e8)
- update metadata [`95e6bc2`](https://github.com/inspect-js/is-async-function/commit/95e6bc2cc195ff5d2ab01f47c6a157b3a583d01a)
- check also for `done` and `next` [`6697d29`](https://github.com/inspect-js/is-async-function/commit/6697d29430ac9ce5f55572a2d7762baa1f05a33b)

## v1.0.0 - 2015-06-05

### Commits

- :cat2: implement :star2: [`eaccc68`](https://github.com/inspect-js/is-async-function/commit/eaccc681838a983390e93607451500982759bd7a)
- add keywords [`55a5ffc`](https://github.com/inspect-js/is-async-function/commit/55a5ffc65d344328cf5b5bb7b7e1520ecede0035)
- Release v1.0.0 [`66eab5f`](https://github.com/inspect-js/is-async-function/commit/66eab5f96c62a6a39b913a84e8ec4b37c657026a)
- refactor [`a7ce00d`](https://github.com/inspect-js/is-async-function/commit/a7ce00d537bf420338b91fee00eb6893d14952bf)
- add test for when throw [`60d0175`](https://github.com/inspect-js/is-async-function/commit/60d0175a955645b304f572fd571a1ced47486958)
- add `related` section [`904acd8`](https://github.com/inspect-js/is-async-function/commit/904acd8fbd5c6c020ba537cc9962154d818ad067)
- simplify travis [`ee17273`](https://github.com/inspect-js/is-async-function/commit/ee172737486a8a5f7b2b642aa72e6ca7a1749a1c)
- Initial commit [`7e914c1`](https://github.com/inspect-js/is-async-function/commit/7e914c1c6d669635f239fa86d9d96f85d8aaaab4)
