{"name": "is-finite", "version": "1.1.0", "description": "ES2015 Number.isFinite() ponyfill", "license": "MIT", "repository": "sindresorhus/is-finite", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "ava"}, "files": ["index.js"], "keywords": ["es2015", "ponyfill", "polyfill", "shim", "number", "finite", "is"], "devDependencies": {"ava": "^3.2.0"}}