<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.GoodsOrderDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.goods_id as goodsId
        ,a.yonghu_id as yonghuId
        ,a.goods_order_time as goodsOrderTime
        ,a.goods_order_types as goodsOrderTypes
        ,a.insert_time as insertTime
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.GoodsOrderView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段
        ,goods.goods_name as goodsName
        ,goods.goods_types as goodsTypes
        ,goods.goods_photo as goodsPhoto
        ,goods.goods_clicknum as goodsClicknum
        ,goods.zan_number as zanNumber
        ,goods.cai_number as caiNumber
        ,goods.shangxia_types as shangxiaTypes
        ,goods.goods_content as goodsContent
        ,yonghu.yonghu_name as yonghuName
        ,yonghu.yonghu_photo as yonghuPhoto
        ,yonghu.yonghu_phone as yonghuPhone
        ,yonghu.yonghu_email as yonghuEmail
        ,yonghu.new_money as newMoney
        ,yonghu.yonghu_delete as yonghuDelete

        FROM goods_order  a
        left JOIN goods goods ON a.goods_id = goods.id
        left JOIN yonghu yonghu ON a.yonghu_id = yonghu.id

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test="params.goodsId != null and params.goodsId != ''">
                and (
                    a.goods_id = #{params.goodsId}
                )
            </if>
            <if test="params.yonghuId != null and params.yonghuId != ''">
                and (
                    a.yonghu_id = #{params.yonghuId}
                )
            </if>
            <if test=" params.goodsOrderTimeStart != '' and params.goodsOrderTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.goods_order_time) >= UNIX_TIMESTAMP(#{params.goodsOrderTimeStart}) ]]>
            </if>
            <if test=" params.goodsOrderTimeEnd != '' and params.goodsOrderTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.goods_order_time) <= UNIX_TIMESTAMP(#{params.goodsOrderTimeEnd}) ]]>
            </if>
            <if test="params.goodsOrderTypes != null and params.goodsOrderTypes != ''">
                and a.goods_order_types = #{params.goodsOrderTypes}
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>

                <!-- 判断商品信息的id不为空 -->
            <if test=" params.goodsIdNotNull != '' and params.goodsIdNotNull != null and params.goodsIdNotNull != 'null' ">
                and a.goods_id IS NOT NULL
            </if>
            <if test=" params.goodsName != '' and params.goodsName != null and params.goodsName != 'null' ">
                and goods.goods_name like CONCAT('%',#{params.goodsName},'%')
            </if>
            <if test="params.goodsTypes != null  and params.goodsTypes != ''">
                and goods.goods_types = #{params.goodsTypes}
            </if>

            <if test="params.goodsClicknumStart != null  and params.goodsClicknumStart != '' ">
                <![CDATA[  and goods.goods_clicknum >= #{params.goodsClicknumStart}   ]]>
            </if>
            <if test="params.goodsClicknumEnd != null  and params.goodsClicknumEnd != '' ">
                <![CDATA[  and goods.goods_clicknum <= #{params.goodsClicknumEnd}   ]]>
            </if>
            <if test="params.goodsClicknum != null  and params.goodsClicknum != '' ">
                and goods.goods_clicknum = #{params.goodsClicknum}
            </if>
            <if test="params.zanNumberStart != null  and params.zanNumberStart != '' ">
                <![CDATA[  and goods.zan_number >= #{params.zanNumberStart}   ]]>
            </if>
            <if test="params.zanNumberEnd != null  and params.zanNumberEnd != '' ">
                <![CDATA[  and goods.zan_number <= #{params.zanNumberEnd}   ]]>
            </if>
            <if test="params.zanNumber != null  and params.zanNumber != '' ">
                and goods.zan_number = #{params.zanNumber}
            </if>
            <if test="params.caiNumberStart != null  and params.caiNumberStart != '' ">
                <![CDATA[  and goods.cai_number >= #{params.caiNumberStart}   ]]>
            </if>
            <if test="params.caiNumberEnd != null  and params.caiNumberEnd != '' ">
                <![CDATA[  and goods.cai_number <= #{params.caiNumberEnd}   ]]>
            </if>
            <if test="params.caiNumber != null  and params.caiNumber != '' ">
                and goods.cai_number = #{params.caiNumber}
            </if>
            <if test="params.shangxiaTypes != null  and params.shangxiaTypes != ''">
                and goods.shangxia_types = #{params.shangxiaTypes}
            </if>

            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(goods.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(goods.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test=" params.goodsContent != '' and params.goodsContent != null and params.goodsContent != 'null' ">
                and goods.goods_content like CONCAT('%',#{params.goodsContent},'%')
            </if>
                <!-- 判断用户的id不为空 -->
            <if test=" params.yonghuIdNotNull != '' and params.yonghuIdNotNull != null and params.yonghuIdNotNull != 'null' ">
                and a.yonghu_id IS NOT NULL
            </if>
            <if test=" params.yonghuName != '' and params.yonghuName != null and params.yonghuName != 'null' ">
                and yonghu.yonghu_name like CONCAT('%',#{params.yonghuName},'%')
            </if>
            <if test=" params.yonghuPhone != '' and params.yonghuPhone != null and params.yonghuPhone != 'null' ">
                and yonghu.yonghu_phone like CONCAT('%',#{params.yonghuPhone},'%')
            </if>
            <if test=" params.yonghuEmail != '' and params.yonghuEmail != null and params.yonghuEmail != 'null' ">
                and yonghu.yonghu_email like CONCAT('%',#{params.yonghuEmail},'%')
            </if>
            <if test="params.newMoneyStart != null ">
                <![CDATA[  and yonghu.new_money >= #{params.newMoneyStart}   ]]>
            </if>
            <if test="params.newMoneyEnd != null ">
                <![CDATA[  and yonghu.new_money <= #{params.newMoneyEnd}   ]]>
            </if>
            <if test="params.yonghuDeleteStart != null  and params.yonghuDeleteStart != '' ">
                <![CDATA[  and yonghu.yonghu_delete >= #{params.yonghuDeleteStart}   ]]>
            </if>
            <if test="params.yonghuDeleteEnd != null  and params.yonghuDeleteEnd != '' ">
                <![CDATA[  and yonghu.yonghu_delete <= #{params.yonghuDeleteEnd}   ]]>
            </if>
            <if test="params.yonghuDelete != null  and params.yonghuDelete != '' ">
                and yonghu.yonghu_delete = #{params.yonghuDelete}
            </if>
        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>