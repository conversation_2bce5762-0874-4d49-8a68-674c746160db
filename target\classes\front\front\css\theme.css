/*定义全局css*/
body {

	/* 红 */
    /* 1 全局公共主颜色 */
    --publicMainColor: #f06f4f;
    /* 1 全局公共副颜色 */
    --publicSubColor:  #d24a32;

}

/*开始==================================导航栏样式6=========================================开始*/
#iframe {
	width: 100%;
	margin-top: 100px;
	padding-top: 40px;
}
#header {
	height: auto;
	background: #fff;
	border-bottom: 0;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
}

#header .nav-top {
	display: flex;
	align-items: center;
	padding: 0 20px;
	font-size: 16px;
	color: #2a8a15;
	box-sizing: border-box;
	height: 40px;
	background-color: rgba(255, 255, 255, 1);
	box-shadow: 0 1px 0px var(--publicMainColor);
	justify-content: center;
	position: relative;
}

#header .nav-top-img {
	width: 124px;
	height: 40px;
	padding: 0;
	margin: 0;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	box-shadow: 0 0 6px rgba(0,0,0,.3);
}

#header .nav-top-title {
	line-height: 40px;
	font-size: 25px;
	color: var(--publicMainColor);
	padding: 0 10px;
	margin: 0 10px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: var(--publicMainColor);
	box-shadow: 0 0 0px var(--publicMainColor);
}

#header .nav-top-tel {
	line-height: 40px;
	font-size: 25px;
	color: rgba(222, 222, 222, 1);
	padding: 0 10px;
	margin: 0;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(179, 172, 172, 1);
	box-shadow: 0 0 0px rgba(0,0,0,.3);
}

#header .navs {
	display: flex;
	padding: 0 20px;
	align-items: center;
	box-sizing: border-box;
	height: 100px;
	background-color: rgba(255, 255, 255, 1);
	box-shadow: 0 1px 6px rgba(0,0,0,0);
	justify-content: center;
}
#header .navs .title {
	width: auto;
	line-height: 40px;
	font-size: 16px;
	color: #333;
	padding: 0 10px;
	margin: 0 5px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	box-shadow: 0 0 6px rgba(0,0,0,0);
}
#header .navs li {
	display: inline-block;
	width: auto;
	line-height: 34px;
	padding: 33px 5px;
	margin: 0 5px;
	color: rgba(0, 0, 0, 1);
	font-size: 20px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	background-color: $template2.front.base.nav.list.item.backgroundColor;
	box-shadow: 0 0 0px rgba(0,0,0,.1);
	text-align: center;
}
#header .navs li a{
	color: inherit;
}
#header .navs li.current a{
	color: inherit;
}
#header .navs li a:hover{
	color: inherit;
}
#header .navs li.current {
	color: rgba(255, 255, 255, 1);
	font-size: 21px;
	border-radius: 0px;
	border-width: 0;
	border-style: solid;
	border-color: var(--publicMainColor);
	background-color: var(--publicMainColor);
	box-shadow: 0 0 0px var(--publicMainColor);
}
#header .navs li:hover {
	color: var(--publicSubColor);
	font-size: 20px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	background-color: rgba(255, 255, 255, 1);
	box-shadow: 0 0 0px rgba(0,0,0,.3);
}
/*结束==================================导航栏样式6=========================================结束*/

/*home页面数据样式 开始*/
	/*home页面数据样式 普通数据样式 开始*/
.team-agile-img {
	width:100%;
	background-color: #FFF;
	display: block;
	overflow: hidden;
	position: relative;
	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	-ms-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
	opacity: 1;
	filter: alpha(opacity=100);
}
.team-agile-img img {
	width: 100%;
	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	-ms-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
}
.team-agile-img:before {
	content: '';
	background-color: rgba(0, 0, 0, 0);
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	position: absolute;
	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	-ms-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
}
.team-grids{padding:0;}
.team-agile-img:hover .view-caption {
	-moz-transform: translateY(0%) scale(1);
	-o-transform: translateY(0%) scale(1);
	-ms-transform: translateY(0%) scale(1);
	-webkit-transform: translateY(0%) scale(1);
	transform: translateY(0%) scale(1);
}
.view-caption {
	background-color: var(--publicMainColor,orange);
	bottom: 0;
	height: 16%;
	left: 0;
	padding: 15px 20px;
	position: absolute;
	right: 0;
	text-align: left;
	z-index: 99;
	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	-ms-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
	-moz-transform: translateY(150%) scale(1.5);
	-o-transform: translateY(150%) scale(1.5);
	-ms-transform: translateY(150%) scale(1.5);
	-webkit-transform: translateY(150%) scale(1.5);
	transform: translateY(150%) scale(1.5);
}
.view-caption .w3ls-info {
	float: left;
}
.view-caption .w3ls-info h4 {
	color: #fff;
	font-size: 1.5em;
	font-weight: normal;
}
.view-caption .w3ls-info p {
	color: #000;
	font-size: 1em;
}
	/*home页面数据样式 普通数据样式 结束*/
/*home页面数据样式 结束*/

/*list页面数据样式 开始*/
	/*list页面数据样式 普通数据样式 开始*/
	/*list页面数据样式 普通数据样式 结束*/
/*list页面数据样式 结束*/


/* 主页 轮播图选择框颜色 主*/
#test1 .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}
/* 个人中心轮播图 */
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}

/* 大部分颜色 主 */
.main_color {
	color: var(--publicMainColor, #808080);
}
/* 边框颜色 主 */
.main_borderColor{
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}
/* 背景颜色 主 */
.main_backgroundColor {
	background-color: var(--publicMainColor, #808080);
}
/* 登录页面单选按钮颜色 主 */
.l-redio .layui-form-radioed>i {
	font-size: 16px;
	color: var(--publicMainColor, #808080);
}
.l-redio .layui-form-radioed>div {
	font-size: 14px;
	color: var(--publicMainColor, #808080);
}

/* 大部分颜色 副 */
.sub_color {
	color: var(--publicSubColor, #808080);
}
/* 边框颜色 副 */
.sub_borderColor{
	border-color: var(--publicSubColor, #808080);
	box-shadow: 0 0 6px var(--publicSubColor, #808080);
}
/* 背景颜色 副 */
.sub_backgroundColor {
	background-color: var(--publicSubColor, #808080);
}

/* 分页颜色 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: var(--publicMainColor, #808080);
}

/* 评论和简介背景颜色 */
.detail-tab .layui-tab-card>.layui-tab-title .layui-this {
	background-color: var(--publicMainColor, #808080);
	color: #fff;
	font-size: 14px;
}
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
}

/* 个人中心 菜单点击颜色*/
.center-container .layui-nav-tree .layui-nav-item.layui-this {
	background-color: var(--publicSubColor, #808080);
}
/*个人中心 菜单鼠标移上颜色*/
.center-container .layui-nav-tree .layui-nav-item:hover {
	background-color:var(--publicMainColor, #808080);
}
/*个人中心 菜单下线颜色*/
.center-container .layui-nav-tree .layui-nav-item {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 输入框中字体颜色和边框颜色*/
.right-container .input .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 下拉框中字体颜色和边框颜色*/
.right-container .select .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 未知颜色*/
.right-container .date .layui-input {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}

/* 前台elementUI得下拉框内容颜色和边框颜色修改 */
/* start */
.el-select-dropdown__item.selected {
	color: var(--publicMainColor, #808080);
	font-weight: bold;
}
.el-select .el-input.is-focus .el-input__inner {
	border-color: var(--publicMainColor, #808080);
}
.el-input--suffix .el-input__inner{
	color:var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
}
.el-select .el-input__inner:focus {
	border-color: var(--publicMainColor, #808080);
}
/* end */
/*=====================富文本框字体样式===========================================================================*/

.ql-size-small {
	font-size: 10px;
}
.ql-size-large {
	font-size: 18px;
}
.ql-size-huge {
	font-size: 32px;
}