<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.GoodsDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.goods_name as goodsName
        ,a.goods_types as goodsTypes
        ,a.goods_photo as goodsPhoto
        ,a.goods_clicknum as goodsClicknum
        ,a.zan_number as zanNumber
        ,a.cai_number as caiNumber
        ,a.shangxia_types as shangxiaTypes
        ,a.goods_new_money as goodsNewMoney
        ,a.insert_time as insertTime
        ,a.goods_content as goodsContent
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.GoodsView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段

        FROM goods  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.goodsName != '' and params.goodsName != null and params.goodsName != 'null' ">
                and a.goods_name like CONCAT('%',#{params.goodsName},'%')
            </if>
            <if test="params.goodsTypes != null and params.goodsTypes != ''">
                and a.goods_types = #{params.goodsTypes}
            </if>
            <if test="params.goodsClicknumStart != null and params.goodsClicknumStart != ''">
                <![CDATA[  and a.goods_clicknum >= #{params.goodsClicknumStart}   ]]>
            </if>
            <if test="params.goodsClicknumEnd != null and params.goodsClicknumEnd != ''">
                <![CDATA[  and a.goods_clicknum <= #{params.goodsClicknumEnd}   ]]>
            </if>
             <if test="params.goodsClicknum != null and params.goodsClicknum != ''">
                and a.goods_clicknum = #{params.goodsClicknum}
             </if>
            <if test="params.zanNumberStart != null and params.zanNumberStart != ''">
                <![CDATA[  and a.zan_number >= #{params.zanNumberStart}   ]]>
            </if>
            <if test="params.zanNumberEnd != null and params.zanNumberEnd != ''">
                <![CDATA[  and a.zan_number <= #{params.zanNumberEnd}   ]]>
            </if>
             <if test="params.zanNumber != null and params.zanNumber != ''">
                and a.zan_number = #{params.zanNumber}
             </if>
            <if test="params.caiNumberStart != null and params.caiNumberStart != ''">
                <![CDATA[  and a.cai_number >= #{params.caiNumberStart}   ]]>
            </if>
            <if test="params.caiNumberEnd != null and params.caiNumberEnd != ''">
                <![CDATA[  and a.cai_number <= #{params.caiNumberEnd}   ]]>
            </if>
             <if test="params.caiNumber != null and params.caiNumber != ''">
                and a.cai_number = #{params.caiNumber}
             </if>
            <if test="params.shangxiaTypes != null and params.shangxiaTypes != ''">
                and a.shangxia_types = #{params.shangxiaTypes}
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test=" params.goodsContent != '' and params.goodsContent != null and params.goodsContent != 'null' ">
                and a.goods_content like CONCAT('%',#{params.goodsContent},'%')
            </if>

        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>