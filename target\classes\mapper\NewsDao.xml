<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.NewsDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.news_name as newsName
        ,a.news_types as newsTypes
        ,a.news_photo as newsPhoto
        ,a.insert_time as insertTime
        ,a.news_content as newsContent
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.NewsView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段

        FROM news  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.newsName != '' and params.newsName != null and params.newsName != 'null' ">
                and a.news_name like CONCAT('%',#{params.newsName},'%')
            </if>
            <if test="params.newsTypes != null and params.newsTypes != ''">
                and a.news_types = #{params.newsTypes}
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test=" params.newsContent != '' and params.newsContent != null and params.newsContent != 'null' ">
                and a.news_content like CONCAT('%',#{params.newsContent},'%')
            </if>

        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>