<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.YonghuDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.username as username
        ,a.password as password
        ,a.yonghu_name as yonghu<PERSON><PERSON>
        ,a.yonghu_photo as yonghu<PERSON>hoto
        ,a.yonghu_phone as yonghuPhone
        ,a.yonghu_email as yonghuEmail
        ,a.sex_types as sexTypes
        ,a.new_money as newMoney
        ,a.yonghu_delete as yonghuDelete
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.YonghuView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段

        FROM yonghu  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.username != '' and params.username != null and params.username != 'null' ">
                and a.username like CONCAT('%',#{params.username},'%')
            </if>
            <if test=" params.password != '' and params.password != null and params.password != 'null' ">
                and a.password like CONCAT('%',#{params.password},'%')
            </if>
            <if test=" params.yonghuName != '' and params.yonghuName != null and params.yonghuName != 'null' ">
                and a.yonghu_name like CONCAT('%',#{params.yonghuName},'%')
            </if>
            <if test=" params.yonghuPhone != '' and params.yonghuPhone != null and params.yonghuPhone != 'null' ">
                and a.yonghu_phone like CONCAT('%',#{params.yonghuPhone},'%')
            </if>
            <if test=" params.yonghuEmail != '' and params.yonghuEmail != null and params.yonghuEmail != 'null' ">
                and a.yonghu_email like CONCAT('%',#{params.yonghuEmail},'%')
            </if>
            <if test="params.sexTypes != null and params.sexTypes != ''">
                and a.sex_types = #{params.sexTypes}
            </if>
            <if test="params.newMoneyStart != null ">
                <![CDATA[  and a.new_money >= #{params.newMoneyStart}   ]]>
            </if>
            <if test="params.newMoneyEnd != null ">
                <![CDATA[  and a.new_money <= #{params.newMoneyEnd}   ]]>
            </if>
            <if test="params.yonghuDeleteStart != null and params.yonghuDeleteStart != ''">
                <![CDATA[  and a.yonghu_delete >= #{params.yonghuDeleteStart}   ]]>
            </if>
            <if test="params.yonghuDeleteEnd != null and params.yonghuDeleteEnd != ''">
                <![CDATA[  and a.yonghu_delete <= #{params.yonghuDeleteEnd}   ]]>
            </if>
             <if test="params.yonghuDelete != null and params.yonghuDelete != ''">
                and a.yonghu_delete = #{params.yonghuDelete}
             </if>

        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>