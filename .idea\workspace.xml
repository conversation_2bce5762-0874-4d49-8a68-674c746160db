<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d968a515-1ed1-43cd-b20b-e6443babba47" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\BrowserDownloads\apache-maven-3.5.0" />
        <option name="localRepository" value="E:\BrowserDownloads\apache-maven-3.5.0\repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="E:\BrowserDownloads\apache-maven-3.5.0\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="32B7TY06o2YJyKXuXajVYKQzhf1" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.qicheweixiuyuyuefuwuApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/JavaFinsh/Design-and-Implementation-of-Automobile-Maintenance-Reservation-Service-System-main",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "configurable.group.other",
    "ts.external.directory.path": "E:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.3.5\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="Design-and-Implementation-of-Automobile-Maintenance-Reservation-Service-System-main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="Design-and-Implementation-of-Automobile-Maintenance-Reservation-Service-System-main" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="qicheweixiuyuyuefuwuApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="qicheweixiuyuyuefuwu" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qicheweixiuyuyuefuwuApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="d968a515-1ed1-43cd-b20b-e6443babba47" name="更改" comment="" />
      <created>1750178359785</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750178359785</updated>
      <workItem from="1756882121046" duration="1150000" />
      <workItem from="1756898191181" duration="52000" />
      <workItem from="1757912872002" duration="1076000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>