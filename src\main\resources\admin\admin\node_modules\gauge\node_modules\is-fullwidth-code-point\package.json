{"name": "is-fullwidth-code-point", "version": "1.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": "sindresorhus/is-fullwidth-code-point", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "dependencies": {"number-is-nan": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "code-point-at": "^1.0.0"}}