!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.zrender={})}(this,function(t){"use strict";function r(){return e++}var e=2311,_="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:function(t){var e={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),n=t.match(/Edge\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(e.firefox=!0,e.version=i[1]);r&&(e.ie=!0,e.version=r[1]);n&&(e.edge=!0,e.version=n[1]);a&&(e.weChat=!0);return{browser:e,os:{},node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!e.ie&&!e.edge,pointerEventsSupported:"onpointerdown"in window&&(e.edge||e.ie&&11<=e.version),domSupported:"undefined"!=typeof document}}(navigator.userAgent);var s={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},h={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},l=Object.prototype.toString,i=Array.prototype,o=i.forEach,u=i.filter,n=i.slice,c=i.map,f=i.reduce,a={};function d(t){if(null==t||"object"!=typeof t)return t;var e=t,i=l.call(t);if("[object Array]"===i){if(!H(t)){e=[];for(var r=0,n=t.length;r<n;r++)e[r]=d(t[r])}}else if(h[i]){if(!H(t)){var a=t.constructor;if(t.constructor.from)e=a.from(t);else{e=new a(t.length);for(r=0,n=t.length;r<n;r++)e[r]=d(t[r])}}}else if(!s[i]&&!H(t)&&!B(t))for(var o in e={},t)t.hasOwnProperty(o)&&(e[o]=d(t[o]));return e}function p(t,e,i){if(!L(e)||!L(t))return i?d(e):t;for(var r in e)if(e.hasOwnProperty(r)){var n=t[r],a=e[r];!L(a)||!L(n)||M(a)||M(n)||B(a)||B(n)||z(a)||z(n)||H(a)||H(n)?!i&&r in t||(t[r]=d(e[r])):p(n,a,i)}return t}function g(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function v(t,e,i){for(var r in e)e.hasOwnProperty(r)&&(i?null!=e[r]:null==t[r])&&(t[r]=e[r]);return t}function m(){return a.createCanvas()}var y;function x(){return y=y||m().getContext("2d")}function w(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var i=0,r=t.length;i<r;i++)if(t[i]===e)return i}return-1}function b(t,e){var i=t.prototype;function r(){}for(var n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function k(t,e,i){v(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,i)}function D(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function T(t,e,i){if(t&&e)if(t.forEach&&t.forEach===o)t.forEach(e,i);else if(t.length===+t.length)for(var r=0,n=t.length;r<n;r++)e.call(i,t[r],r,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function S(t,e,i){if(t&&e){if(t.map&&t.map===c)return t.map(e,i);for(var r=[],n=0,a=t.length;n<a;n++)r.push(e.call(i,t[n],n,t));return r}}function C(t,e){var i=n.call(arguments,2);return function(){return t.apply(e,i.concat(n.call(arguments)))}}function M(t){return"[object Array]"===l.call(t)}function A(t){return"function"==typeof t}function P(t){return"[object String]"===l.call(t)}function L(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function z(t){return!!s[l.call(t)]}function B(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function I(t,e){return null!=t?t:e}function O(t,e,i){return null!=t?t:null!=e?e:i}function R(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function E(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}a.createCanvas=function(){return document.createElement("canvas")};var F="__ec_primitive__";function H(t){return t[F]}function N(t){var i=M(t);this.data={};var r=this;function e(t,e){i?r.set(t,e):r.set(e,t)}t instanceof N?t.each(e):t&&T(t,e)}function W(){}N.prototype={constructor:N,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var i in void 0!==e&&(t=C(t,e)),this.data)this.data.hasOwnProperty(i)&&t(this.data[i],i)},removeKey:function(t){delete this.data[t]}};var V=(Object.freeze||Object)({$override:function(t,e){"createCanvas"===t&&(y=null),a[t]=e},clone:d,merge:p,mergeAll:function(t,e){for(var i=t[0],r=1,n=t.length;r<n;r++)i=p(i,t[r],e);return i},extend:g,defaults:v,createCanvas:m,getContext:x,indexOf:w,inherits:b,mixin:k,isArrayLike:D,each:T,map:S,reduce:function(t,e,i,r){if(t&&e){if(t.reduce&&t.reduce===f)return t.reduce(e,i,r);for(var n=0,a=t.length;n<a;n++)i=e.call(r,i,t[n],n,t);return i}},filter:function(t,e,i){if(t&&e){if(t.filter&&t.filter===u)return t.filter(e,i);for(var r=[],n=0,a=t.length;n<a;n++)e.call(i,t[n],n,t)&&r.push(t[n]);return r}},find:function(t,e,i){if(t&&e)for(var r=0,n=t.length;r<n;r++)if(e.call(i,t[r],r,t))return t[r]},bind:C,curry:function(t){var e=n.call(arguments,1);return function(){return t.apply(this,e.concat(n.call(arguments)))}},isArray:M,isFunction:A,isString:P,isObject:L,isBuiltInObject:z,isTypedArray:function(t){return!!h[l.call(t)]},isDom:B,eqNaN:function(t){return t!=t},retrieve:function(t){for(var e=0,i=arguments.length;e<i;e++)if(null!=arguments[e])return arguments[e]},retrieve2:I,retrieve3:O,slice:function(){return Function.call.apply(n,arguments)},normalizeCssArray:R,assert:function(t,e){if(!t)throw new Error(e)},trim:E,setAsPrimitive:function(t){t[F]=!0},isPrimitive:H,createHashMap:function(t){return new N(t)},concatArray:function(t,e){for(var i=new t.constructor(t.length+e.length),r=0;r<t.length;r++)i[r]=t[r];var n=t.length;for(r=0;r<e.length;r++)i[r+n]=e[r];return i},noop:W}),X="undefined"==typeof Float32Array?Array:Float32Array;function q(t,e){var i=new X(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function j(t){var e=new X(2);return e[0]=t[0],e[1]=t[1],e}function Y(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t}function U(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function G(t){return Math.sqrt(Q(t))}var Z=G;function Q(t){return t[0]*t[0]+t[1]*t[1]}var $=Q;function K(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t}function J(t,e){var i=G(e);return 0===i?(t[0]=0,t[1]=0):(t[0]=e[0]/i,t[1]=e[1]/i),t}function tt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var et=tt;function it(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var rt=it;function nt(t,e,i){var r=e[0],n=e[1];return t[0]=i[0]*r+i[2]*n+i[4],t[1]=i[1]*r+i[3]*n+i[5],t}function at(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t}function ot(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}var st=(Object.freeze||Object)({create:q,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:j,set:function(t,e,i){return t[0]=e,t[1]=i,t},add:Y,scaleAndAdd:function(t,e,i,r){return t[0]=e[0]+i[0]*r,t[1]=e[1]+i[1]*r,t},sub:U,len:G,length:Z,lenSquare:Q,lengthSquare:$,mul:function(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t},div:function(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:K,normalize:J,distance:tt,dist:et,distanceSquare:it,distSquare:rt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,i,r){return t[0]=e[0]+r*(i[0]-e[0]),t[1]=e[1]+r*(i[1]-e[1]),t},applyTransform:nt,min:at,max:ot});function ht(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function lt(t,e){return{target:t,topTarget:e&&e.topTarget}}ht.prototype={constructor:ht,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(lt(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,r=t.offsetY,n=i-this._x,a=r-this._y;this._x=i,this._y=r,e.drift(n,a,t),this.dispatchToElement(lt(e,t),"drag",t.event);var o=this.findHover(i,r,e).target,s=this._dropTarget;e!==(this._dropTarget=o)&&(s&&o!==s&&this.dispatchToElement(lt(s,t),"dragleave",t.event),o&&o!==s&&this.dispatchToElement(lt(o,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(lt(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(lt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};function ut(t){this._$handlers={},this._$eventProcessor=t}var ct=Array.prototype.slice;function ft(t,e,i,r,n,a){var o=t._$handlers;if("function"==typeof i&&(n=r,r=i,i=null),!r||!e)return t;i=function(t,e){var i=t._$eventProcessor;return null!=e&&i&&i.normalizeQuery&&(e=i.normalizeQuery(e)),e}(t,i),o[e]||(o[e]=[]);for(var s=0;s<o[e].length;s++)if(o[e][s].h===r)return t;var h={h:r,one:a,query:i,ctx:n||t,callAtLast:r.zrEventfulCallAtLast},l=o[e].length-1,u=o[e][l];return u&&u.callAtLast?o[e].splice(l,0,h):o[e].push(h),t}ut.prototype={constructor:ut,one:function(t,e,i,r){return ft(this,t,e,i,r,!0)},on:function(t,e,i,r){return ft(this,t,e,i,r,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var r=[],n=0,a=i[t].length;n<a;n++)i[t][n].h!==e&&r.push(i[t][n]);i[t]=r}i[t]&&0===i[t].length&&delete i[t]}else delete i[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var r=arguments,n=r.length;3<n&&(r=ct.call(r,1));for(var a=e.length,o=0;o<a;){var s=e[o];if(i&&i.filter&&null!=s.query&&!i.filter(t,s.query))o++;else{switch(n){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,r[1]);break;case 3:s.h.call(s.ctx,r[1],r[2]);break;default:s.h.apply(s.ctx,r)}s.one?(e.splice(o,1),a--):o++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var r=arguments,n=r.length;4<n&&(r=ct.call(r,1,r.length-1));for(var a=r[r.length-1],o=e.length,s=0;s<o;){var h=e[s];if(i&&i.filter&&null!=h.query&&!i.filter(t,h.query))s++;else{switch(n){case 1:h.h.call(a);break;case 2:h.h.call(a,r[1]);break;case 3:h.h.call(a,r[1],r[2]);break;default:h.h.apply(a,r)}h.one?(e.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var dt=Math.log(2);function pt(t,e,i,r,n,a){var o=r+"-"+n,s=t.length;if(a.hasOwnProperty(o))return a[o];if(1===e){var h=Math.round(Math.log((1<<s)-1&~n)/dt);return t[i][h]}for(var l=r|1<<i,u=i+1;r&1<<u;)u++;for(var c=0,f=0,d=0;f<s;f++){var p=1<<f;p&n||(c+=(d%2?-1:1)*t[i][f]*pt(t,e-1,u,l,n|p,a),d++)}return a[o]=c}function gt(t,e){var i=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],r={},n=pt(i,8,0,0,0,r);if(0!==n){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)null==a[s]&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*pt(i,7,0===o?1:0,1<<o,1<<s,r)/n*e[o];return function(t,e,i){var r=e*a[6]+i*a[7]+1;t[0]=(e*a[0]+i*a[1]+a[2])/r,t[1]=(e*a[3]+i*a[4]+a[5])/r}}}var vt="___zrEVENTSAVED";function _t(t,e,i,r,n){if(e.getBoundingClientRect&&_.domSupported&&!mt(e)){var a=e[vt]||(e[vt]={}),o=function(t,e,i){for(var r=i?"invTrans":"trans",n=e[r],a=e.srcCoords,o=!0,s=[],h=[],l=0;l<4;l++){var u=t[l].getBoundingClientRect(),c=2*l,f=u.left,d=u.top;s.push(f,d),o=o&&a&&f===a[c]&&d===a[1+c],h.push(t[l].offsetLeft,t[l].offsetTop)}return o&&n?n:(e.srcCoords=s,e[r]=i?gt(h,s):gt(s,h))}(function(t,e){var i=e.markers;if(i)return i;i=e.markers=[];for(var r=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,h=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",r[h]+":0",n[l]+":0",r[1-h]+":auto",n[1-l]+":auto",""].join("!important;"),t.appendChild(o),i.push(o)}return i}(e,a),a,n);if(o)return o(t,i,r),!0}return!1}function mt(t){return"CANVAS"===t.nodeName.toUpperCase()}var yt="undefined"!=typeof window&&!!window.addEventListener,xt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,wt=[];function bt(t,e,i,r){return i=i||{},r||!_.canvasSupported?kt(t,e,i):_.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):kt(t,e,i),i}function kt(t,e,i){if(_.domSupported&&t.getBoundingClientRect){var r=e.clientX,n=e.clientY;if(mt(t)){var a=t.getBoundingClientRect();return i.zrX=r-a.left,void(i.zrY=n-a.top)}if(_t(wt,t,r,n))return i.zrX=wt[0],void(i.zrY=wt[1])}i.zrX=i.zrY=0}function Tt(t){return t||window.event}function St(t,e,i){if(null!=(e=Tt(e)).zrX)return e;var r=e.type;if(r&&0<=r.indexOf("touch")){var n="touchend"!==r?e.targetTouches[0]:e.changedTouches[0];n&&bt(t,n,e,i)}else bt(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var a=e.button;return null==e.which&&void 0!==a&&xt.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}function Ct(){this._track=[]}var Mt=yt?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function At(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}Ct.prototype={constructor:Ct,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,i){var r=t.touches;if(r){for(var n={points:[],touches:[],target:e,event:t},a=0,o=r.length;a<o;a++){var s=r[a],h=bt(i,s,{});n.points.push([h.zrX,h.zrY]),n.touches.push(s)}this._track.push(n)}},_recognize:function(t){for(var e in Pt)if(Pt.hasOwnProperty(e)){var i=Pt[e](this._track,t);if(i)return i}}};var Pt={pinch:function(t,e){var i=t.length;if(i){var r=(t[i-1]||{}).points,n=(t[i-2]||{}).points||r;if(n&&1<n.length&&r&&1<r.length){var a=At(r)/At(n);isFinite(a)||(a=1),e.pinchScale=a;var o=function(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}(r);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},Lt="silent";function zt(){Mt(this.event)}function Dt(){}Dt.prototype.dispose=function(){};function Bt(t,e,i,r){ut.call(this),this.storage=t,this.painter=e,this.painterRoot=r,i=i||new Dt,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,ht.call(this),this.setHandlerProxy(i)}var It=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"];function Ot(t,e,i){if(t[t.rectHover?"rectContain":"contain"](e,i)){for(var r,n=t;n;){if(n.clipPath&&!n.clipPath.contain(e,i))return!1;n.silent&&(r=!0),n=n.parent}return!r||Lt}return!1}function Rt(t,e,i){var r=t.painter;return e<0||e>r.getWidth()||i<0||i>r.getHeight()}Bt.prototype={constructor:Bt,setHandlerProxy:function(e){this.proxy&&this.proxy.dispose(),e&&(T(It,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},mousemove:function(t){var e=t.zrX,i=t.zrY,r=Rt(this,e,i),n=this._hovered,a=n.target;a&&!a.__zr&&(a=(n=this.findHover(n.x,n.y)).target);var o=this._hovered=r?{x:e,y:i}:this.findHover(e,i),s=o.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),a&&s!==a&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(o,"mousemove",t),s&&s!==a&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,i=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&(i||this.trigger("globalout",{type:"globalout",event:t}))},resize:function(t){this._hovered={}},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,i){var r=(t=t||{}).target;if(!r||!r.silent){for(var n="on"+e,a=function(t,e,i){return{type:t,event:i,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch,which:i.which,stop:zt}}(e,t,i);r&&(r[n]&&(a.cancelBubble=r[n].call(r,a)),r.trigger(e,a),r=r.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[n]&&t[n].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,i){for(var r=this.storage.getDisplayList(),n={x:t,y:e},a=r.length-1;0<=a;a--){var o;if(r[a]!==i&&!r[a].ignore&&(o=Ot(r[a],t,e))&&(n.topTarget||(n.topTarget=r[a]),o!==Lt)){n.target=r[a];break}}return n},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new Ct);var i=this._gestureMgr;"start"===e&&i.clear();var r=i.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&i.clear(),r){var n=r.type;t.gestureEvent=n,this.dispatchToElement({target:r.target},n,r.event)}}},T(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(o){Bt.prototype[o]=function(t){var e,i,r=t.zrX,n=t.zrY,a=Rt(this,r,n);if("mouseup"===o&&a||(i=(e=this.findHover(r,n)).target),"mousedown"===o)this._downEl=i,this._downPoint=[t.zrX,t.zrY],this._upEl=i;else if("mouseup"===o)this._upEl=i;else if("click"===o){if(this._downEl!==this._upEl||!this._downPoint||4<et(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,o,t)}}),k(Bt,ut),k(Bt,ht);var Et="undefined"==typeof Float32Array?Array:Float32Array;function Ft(){var t=new Et(6);return Ht(t),t}function Ht(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Nt(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Wt(t,e,i){var r=e[0]*i[0]+e[2]*i[1],n=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],h=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=r,t[1]=n,t[2]=a,t[3]=o,t[4]=s,t[5]=h,t}function Vt(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function Xt(t,e,i){var r=e[0],n=e[2],a=e[4],o=e[1],s=e[3],h=e[5],l=Math.sin(i),u=Math.cos(i);return t[0]=r*u+o*l,t[1]=-r*l+o*u,t[2]=n*u+s*l,t[3]=-n*l+u*s,t[4]=u*a+l*h,t[5]=u*h-l*a,t}function qt(t,e,i){var r=i[0],n=i[1];return t[0]=e[0]*r,t[1]=e[1]*n,t[2]=e[2]*r,t[3]=e[3]*n,t[4]=e[4]*r,t[5]=e[5]*n,t}function jt(t,e){var i=e[0],r=e[2],n=e[4],a=e[1],o=e[3],s=e[5],h=i*o-a*r;return h?(h=1/h,t[0]=o*h,t[1]=-a*h,t[2]=-r*h,t[3]=i*h,t[4]=(r*s-o*n)*h,t[5]=(a*n-i*s)*h,t):null}var Yt=(Object.freeze||Object)({create:Ft,identity:Ht,copy:Nt,mul:Wt,translate:Vt,rotate:Xt,scale:qt,invert:jt,clone:function(t){var e=Ft();return Nt(e,t),e}}),Ut=Ht;function Gt(t){return 5e-5<t||t<-5e-5}function Zt(t){(t=t||{}).position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null}var Qt=Zt.prototype;Qt.transform=null,Qt.needLocalTransform=function(){return Gt(this.rotation)||Gt(this.position[0])||Gt(this.position[1])||Gt(this.scale[0]-1)||Gt(this.scale[1]-1)};var $t=[];Qt.updateTransform=function(){var t=this.parent,e=t&&t.transform,i=this.needLocalTransform(),r=this.transform;if(i||e){r=r||Ft(),i?this.getLocalTransform(r):Ut(r),e&&(i?Wt(r,t.transform,r):Nt(r,t.transform)),this.transform=r;var n=this.globalScaleRatio;if(null!=n&&1!==n){this.getGlobalScale($t);var a=$t[0]<0?-1:1,o=$t[1]<0?-1:1,s=(($t[0]-a)*n+a)/$t[0]||0,h=(($t[1]-o)*n+o)/$t[1]||0;r[0]*=s,r[1]*=s,r[2]*=h,r[3]*=h}this.invTransform=this.invTransform||Ft(),jt(this.invTransform,r)}else r&&Ut(r)},Qt.getLocalTransform=function(t){return Zt.getLocalTransform(this,t)},Qt.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},Qt.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var Kt=[],Jt=Ft();Qt.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],r=this.position,n=this.scale;Gt(e-1)&&(e=Math.sqrt(e)),Gt(i-1)&&(i=Math.sqrt(i)),t[0]<0&&(e=-e),t[3]<0&&(i=-i),r[0]=t[4],r[1]=t[5],n[0]=e,n[1]=i,this.rotation=Math.atan2(-t[1]/i,t[0]/e)}},Qt.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(Wt(Kt,t.invTransform,e),e=Kt);var i=this.origin;i&&(i[0]||i[1])&&(Jt[4]=i[0],Jt[5]=i[1],Wt(Kt,e,Jt),Kt[4]-=i[0],Kt[5]-=i[1],e=Kt),this.setLocalTransform(e)}},Qt.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},Qt.transformCoordToLocal=function(t,e){var i=[t,e],r=this.invTransform;return r&&nt(i,i,r),i},Qt.transformCoordToGlobal=function(t,e){var i=[t,e],r=this.transform;return r&&nt(i,i,r),i},Zt.getLocalTransform=function(t,e){Ut(e=e||[]);var i=t.origin,r=t.scale||[1,1],n=t.rotation||0,a=t.position||[0,0];return i&&(e[4]-=i[0],e[5]-=i[1]),qt(e,e,r),n&&Xt(e,e,n),i&&(e[4]+=i[0],e[5]+=i[1]),e[4]+=a[0],e[5]+=a[1],e};var te={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),-i*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?i*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:i*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-te.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*te.bounceIn(2*t):.5*te.bounceOut(2*t-1)+.5}};function ee(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}ee.prototype={constructor:ee,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var i=(t-this._startTime-this._pausedTime)/this._life;if(!(i<0)){i=Math.min(i,1);var r=this.easing,n="string"==typeof r?te[r]:r,a="function"==typeof n?n(i):i;return this.fire("frame",a),1===i?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){this[t="on"+t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};function ie(){this.head=null,this.tail=null,this._len=0}var re=ie.prototype;re.insert=function(t){var e=new ae(t);return this.insertEntry(e),e},re.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},re.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},re.len=function(){return this._len},re.clear=function(){this.head=this.tail=null,this._len=0};function ne(t){this._list=new ie,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null}var ae=function(t){this.value=t,this.next,this.prev},oe=ne.prototype;oe.put=function(t,e){var i=this._list,r=this._map,n=null;if(null==r[t]){var a=i.len(),o=this._lastRemovedEntry;if(a>=this._maxSize&&0<a){var s=i.head;i.remove(s),delete r[s.key],n=s.value,this._lastRemovedEntry=s}o?o.value=e:o=new ae(e),o.key=t,i.insertEntry(o),r[t]=o}return n},oe.get=function(t){var e=this._map[t],i=this._list;if(null!=e)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},oe.clear=function(){this._list.clear(),this._map={}};var se={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function he(t){return(t=Math.round(t))<0?0:255<t?255:t}function le(t){return t<0?0:1<t?1:t}function ue(t){return t.length&&"%"===t.charAt(t.length-1)?he(parseFloat(t)/100*255):he(parseInt(t,10))}function ce(t){return t.length&&"%"===t.charAt(t.length-1)?le(parseFloat(t)/100):le(parseFloat(t))}function fe(t,e,i){return i<0?i+=1:1<i&&(i-=1),6*i<1?t+(e-t)*i*6:2*i<1?e:3*i<2?t+(e-t)*(2/3-i)*6:t}function de(t,e,i){return t+(e-t)*i}function pe(t,e,i,r,n){return t[0]=e,t[1]=i,t[2]=r,t[3]=n,t}function ge(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var ve=new ne(20),_e=null;function me(t,e){_e&&ge(_e,e),_e=ve.put(t,_e||e.slice())}function ye(t,e){if(t){e=e||[];var i=ve.get(t);if(i)return ge(e,i);var r,n=(t+="").replace(/ /g,"").toLowerCase();if(n in se)return ge(e,se[n]),me(t,e),e;if("#"===n.charAt(0))return 4===n.length?0<=(r=parseInt(n.substr(1),16))&&r<=4095?(pe(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,1),me(t,e),e):void pe(e,0,0,0,1):7===n.length?0<=(r=parseInt(n.substr(1),16))&&r<=16777215?(pe(e,(16711680&r)>>16,(65280&r)>>8,255&r,1),me(t,e),e):void pe(e,0,0,0,1):void 0;var a=n.indexOf("("),o=n.indexOf(")");if(-1!==a&&o+1===n.length){var s=n.substr(0,a),h=n.substr(a+1,o-(a+1)).split(","),l=1;switch(s){case"rgba":if(4!==h.length)return void pe(e,0,0,0,1);l=ce(h.pop());case"rgb":return 3!==h.length?void pe(e,0,0,0,1):(pe(e,ue(h[0]),ue(h[1]),ue(h[2]),l),me(t,e),e);case"hsla":return 4!==h.length?void pe(e,0,0,0,1):(h[3]=ce(h[3]),xe(h,e),me(t,e),e);case"hsl":return 3!==h.length?void pe(e,0,0,0,1):(xe(h,e),me(t,e),e);default:return}}pe(e,0,0,0,1)}}function xe(t,e){var i=(parseFloat(t[0])%360+360)%360/360,r=ce(t[1]),n=ce(t[2]),a=n<=.5?n*(r+1):n+r-n*r,o=2*n-a;return pe(e=e||[],he(255*fe(o,a,i+1/3)),he(255*fe(o,a,i)),he(255*fe(o,a,i-1/3)),1),4===t.length&&(e[3]=t[3]),e}function we(t){var e=ye(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function be(t,e,i){if(e&&e.length&&0<=t&&t<=1){i=i||[];var r=t*(e.length-1),n=Math.floor(r),a=Math.ceil(r),o=e[n],s=e[a],h=r-n;return i[0]=he(de(o[0],s[0],h)),i[1]=he(de(o[1],s[1],h)),i[2]=he(de(o[2],s[2],h)),i[3]=le(de(o[3],s[3],h)),i}}var ke=be;function Te(t,e,i){if(e&&e.length&&0<=t&&t<=1){var r=t*(e.length-1),n=Math.floor(r),a=Math.ceil(r),o=ye(e[n]),s=ye(e[a]),h=r-n,l=Ce([he(de(o[0],s[0],h)),he(de(o[1],s[1],h)),he(de(o[2],s[2],h)),le(de(o[3],s[3],h))],"rgba");return i?{color:l,leftIndex:n,rightIndex:a,value:r}:l}}var Se=Te;function Ce(t,e){if(t&&t.length){var i=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(i+=","+t[3]),e+"("+i+")"}}var Me=(Object.freeze||Object)({parse:ye,lift:function(t,e){var i=ye(t);if(i){for(var r=0;r<3;r++)i[r]=e<0?i[r]*(1-e)|0:(255-i[r])*e+i[r]|0,255<i[r]?i[r]=255:t[r]<0&&(i[r]=0);return Ce(i,4===i.length?"rgba":"rgb")}},toHex:we,fastLerp:be,fastMapToColor:ke,lerp:Te,mapToColor:Se,modifyHSL:function(t,e,i,r){if(t=ye(t))return t=function(t){if(t){var e,i,r=t[0]/255,n=t[1]/255,a=t[2]/255,o=Math.min(r,n,a),s=Math.max(r,n,a),h=s-o,l=(s+o)/2;if(0==h)i=e=0;else{i=l<.5?h/(s+o):h/(2-s-o);var u=((s-r)/6+h/2)/h,c=((s-n)/6+h/2)/h,f=((s-a)/6+h/2)/h;r===s?e=f-c:n===s?e=1/3+u-f:a===s&&(e=2/3+c-u),e<0&&(e+=1),1<e&&(e-=1)}var d=[360*e,i,l];return null!=t[3]&&d.push(t[3]),d}}(t),null!=e&&(t[0]=function(t){return(t=Math.round(t))<0?0:360<t?360:t}(e)),null!=i&&(t[1]=ce(i)),null!=r&&(t[2]=ce(r)),Ce(xe(t),"rgba")},modifyAlpha:function(t,e){if((t=ye(t))&&null!=e)return t[3]=le(e),Ce(t,"rgba")},stringify:Ce}),Ae=Array.prototype.slice;function Pe(t,e){return t[e]}function Le(t,e,i){t[e]=i}function ze(t,e,i){return(e-t)*i+t}function De(t,e,i){return.5<i?e:t}function Be(t,e,i,r,n){var a=t.length;if(1===n)for(var o=0;o<a;o++)r[o]=ze(t[o],e[o],i);else{var s=a&&t[0].length;for(o=0;o<a;o++)for(var h=0;h<s;h++)r[o][h]=ze(t[o][h],e[o][h],i)}}function Ie(t,e,i){var r=t.length,n=e.length;if(r!==n)if(n<r)t.length=n;else for(var a=r;a<n;a++)t.push(1===i?e[a]:Ae.call(e[a]));var o=t[0]&&t[0].length;for(a=0;a<t.length;a++)if(1===i)isNaN(t[a])&&(t[a]=e[a]);else for(var s=0;s<o;s++)isNaN(t[a][s])&&(t[a][s]=e[a][s])}function Oe(t,e,i){if(t===e)return!0;var r=t.length;if(r!==e.length)return!1;if(1===i){for(var n=0;n<r;n++)if(t[n]!==e[n])return!1}else{var a=t[0].length;for(n=0;n<r;n++)for(var o=0;o<a;o++)if(t[n][o]!==e[n][o])return!1}return!0}function Re(t,e,i,r,n,a,o,s,h){var l=t.length;if(1===h)for(var u=0;u<l;u++)s[u]=Ee(t[u],e[u],i[u],r[u],n,a,o);else{var c=t[0].length;for(u=0;u<l;u++)for(var f=0;f<c;f++)s[u][f]=Ee(t[u][f],e[u][f],i[u][f],r[u][f],n,a,o)}}function Ee(t,e,i,r,n,a,o){var s=.5*(i-t),h=.5*(r-e);return(2*(e-i)+s+h)*o+(-3*(e-i)-2*s-h)*a+s*n+e}function Fe(t){if(D(t)){var e=t.length;if(D(t[0])){for(var i=[],r=0;r<e;r++)i.push(Ae.call(t[r]));return i}return Ae.call(t)}return t}function He(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function Ne(t,e,i,r,a,n){var o=t._getter,s=t._setter,h="spline"===e,l=r.length;if(l){var u,c=D(r[0].value),f=!1,d=!1,p=c?function(t){var e=t[t.length-1].value;return D(e&&e[0])?2:1}(r):0;r.sort(function(t,e){return t.time-e.time}),u=r[l-1].time;for(var g=[],v=[],_=r[0].value,m=!0,y=0;y<l;y++){g.push(r[y].time/u);var x=r[y].value;if(c&&Oe(x,_,p)||!c&&x===_||(m=!1),"string"==typeof(_=x)){var w=ye(x);w?(x=w,f=!0):d=!0}v.push(x)}if(n||!m){var b=v[l-1];for(y=0;y<l-1;y++)c?Ie(v[y],b,p):!isNaN(v[y])||isNaN(b)||d||f||(v[y]=b);c&&Ie(o(t._target,a),b,p);var k,T,S,C,M,A=0,P=0;if(f)var L=[0,0,0,0];var z=new ee({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:function(t,e){var i;if(e<0)i=0;else if(e<P){for(i=Math.min(A+1,l-1);0<=i&&!(g[i]<=e);i--);i=Math.min(i,l-2)}else{for(i=A;i<l&&!(g[i]>e);i++);i=Math.min(i-1,l-2)}P=e;var r=g[(A=i)+1]-g[i];if(0!=r)if(k=(e-g[i])/r,h)if(S=v[i],T=v[0===i?i:i-1],C=v[l-2<i?l-1:i+1],M=v[l-3<i?l-1:i+2],c)Re(T,S,C,M,k,k*k,k*k*k,o(t,a),p);else{if(f)n=Re(T,S,C,M,k,k*k,k*k*k,L,1),n=He(L);else{if(d)return De(S,C,k);n=Ee(T,S,C,M,k,k*k,k*k*k)}s(t,a,n)}else if(c)Be(v[i],v[i+1],k,o(t,a),p);else{var n;if(f)Be(v[i],v[i+1],k,L,1),n=He(L);else{if(d)return De(v[i],v[i+1],k);n=ze(v[i],v[i+1],k)}s(t,a,n)}},ondestroy:i});return e&&"spline"!==e&&(z.easing=e),z}}}function We(t,e,i,r){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||Pe,this._setter=r||Le,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]}We.prototype={when:function(t,e){var i=this._tracks;for(var r in e)if(e.hasOwnProperty(r)){if(!i[r]){i[r]=[];var n=this._getter(this._target,r);if(null==n)continue;0!==t&&i[r].push({time:0,value:Fe(n)})}i[r].push({time:t,value:e[r]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,i=0;i<e;i++)t[i].call(this)},start:function(t,e){function i(){--a||n._doneCallback()}var r,n=this,a=0;for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=Ne(this,t,i,this._tracks[o],o,e);s&&(this._clipList.push(s),a++,this.animation&&this.animation.addClip(s),r=s)}if(r){var h=r.onframe;r.onframe=function(t,e){h(t,e);for(var i=0;i<n._onframeList.length;i++)n._onframeList[i](t,e)}}return a||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this.animation,r=0;r<e.length;r++){var n=e[r];t&&n.onframe(this._target,1),i&&i.removeClip(n)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var Ve=1;"undefined"!=typeof window&&(Ve=Math.max(window.devicePixelRatio||1,1));var Xe=Ve,qe=function(){};function je(){this.animators=[]}var Ye=qe;function Ue(t,e,i,r,n,a,o,s){P(r)?(a=n,n=r,r=0):A(n)?(a=n,n="linear",r=0):A(r)?(a=r,r=0):i=A(i)?(a=i,500):i||500,t.stopAnimation(),function t(e,i,r,n,a,o,s){var h={};var l=0;for(var u in n)n.hasOwnProperty(u)&&(null!=r[u]?L(n[u])&&!D(n[u])?t(e,i?i+"."+u:u,r[u],n[u],a,o,s):(s?(h[u]=r[u],Ge(e,i,u,n[u])):h[u]=n[u],l++):null==n[u]||s||Ge(e,i,u,n[u]));0<l&&e.animate(i,!1).when(null==a?500:a,h).delay(o||0)}(t,"",t,e,i,r,s);var h=t.animators.slice(),l=h.length;function u(){--l||a&&a()}l||a&&a();for(var c=0;c<h.length;c++)h[c].done(u).start(n,o)}function Ge(t,e,i,r){if(e){var n={};n[e]={},n[e][i]=r,t.attr(n)}else t.attr(i,r)}je.prototype={constructor:je,animate:function(t,e){var i,r=!1,n=this,a=this.__zr;if(t){var o=t.split("."),s=n;r="shape"===o[0];for(var h=0,l=o.length;h<l;h++)s=s&&s[o[h]];s&&(i=s)}else i=n;if(i){var u=n.animators,c=new We(i,e);return c.during(function(t){n.dirty(r)}).done(function(){u.splice(w(u,c),1)}),u.push(c),a&&a.animation.addAnimator(c),c}Ye('Property "'+t+'" is not existed in element '+n.id)},stopAnimation:function(t){for(var e=this.animators,i=e.length,r=0;r<i;r++)e[r].stop(t);return e.length=0,this},animateTo:function(t,e,i,r,n,a){Ue(this,t,e,i,r,n,a)},animateFrom:function(t,e,i,r,n,a){Ue(this,t,e,i,r,n,a,!0)}};var Ze=function(t){Zt.call(this,t),ut.call(this,t),je.call(this,t),this.id=t.id||r()};Ze.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var i=this[t];(i=i||(this[t]=[]))[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(L(t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),(this.clipPath=t).__zr=e,(t.__clipTarget=this).dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},k(Ze,je),k(Ze,Zt),k(Ze,ut);var Qe,$e,Ke,Je,ti=nt,ei=Math.min,ii=Math.max;function ri(t,e,i,r){i<0&&(t+=i,i=-i),r<0&&(e+=r,r=-r),this.x=t,this.y=e,this.width=i,this.height=r}ri.prototype={constructor:ri,union:function(t){var e=ei(t.x,this.x),i=ei(t.y,this.y);this.width=ii(t.x+t.width,this.x+this.width)-e,this.height=ii(t.y+t.height,this.y+this.height)-i,this.x=e,this.y=i},applyTransform:(Qe=[],$e=[],Ke=[],Je=[],function(t){if(t){Qe[0]=Ke[0]=this.x,Qe[1]=Je[1]=this.y,$e[0]=Je[0]=this.x+this.width,$e[1]=Ke[1]=this.y+this.height,ti(Qe,Qe,t),ti($e,$e,t),ti(Ke,Ke,t),ti(Je,Je,t),this.x=ei(Qe[0],$e[0],Ke[0],Je[0]),this.y=ei(Qe[1],$e[1],Ke[1],Je[1]);var e=ii(Qe[0],$e[0],Ke[0],Je[0]),i=ii(Qe[1],$e[1],Ke[1],Je[1]);this.width=e-this.x,this.height=i-this.y}}),calculateTransform:function(t){var e=t.width/this.width,i=t.height/this.height,r=Ft();return Vt(r,r,[-this.x,-this.y]),qt(r,r,[e,i]),Vt(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof ri||(t=ri.create(t));var e=this,i=e.x,r=e.x+e.width,n=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,h=t.y,l=t.y+t.height;return!(r<o||s<i||a<h||l<n)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},clone:function(){return new ri(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}};function ni(t){for(var e in t=t||{},Ze.call(this,t),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0}ni.prototype={constructor:ni,isGroup:!0,type:"group",silent:!(ri.create=function(t){return new ri(t.x,t.y,t.width,t.height)}),children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e.length;i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,r=i.indexOf(e);0<=r&&(i.splice(r,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof ni&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var e=this.__zr,i=this.__storage,r=this._children,n=w(r,t);return n<0||(r.splice(n,1),t.parent=null,i&&(i.delFromStorage(t),t instanceof ni&&t.delChildrenFromStorage(i)),e&&e.refresh()),this},removeAll:function(){var t,e,i=this._children,r=this.__storage;for(e=0;e<i.length;e++)t=i[e],r&&(r.delFromStorage(t),t instanceof ni&&t.delChildrenFromStorage(r)),t.parent=null;return i.length=0,this},eachChild:function(t,e){for(var i=this._children,r=0;r<i.length;r++){var n=i[r];t.call(e,n,r)}return this},traverse:function(t,e){for(var i=0;i<this._children.length;i++){var r=this._children[i];t.call(e,r),"group"===r.type&&r.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.addToStorage(i),i instanceof ni&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.delFromStorage(i),i instanceof ni&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new ri(0,0,0,0),r=t||this._children,n=[],a=0;a<r.length;a++){var o=r[a];if(!o.ignore&&!o.invisible){var s=o.getBoundingRect(),h=o.getLocalTransform(n);h?(i.copy(s),i.applyTransform(h),(e=e||i.clone()).union(i)):(e=e||s.clone()).union(s)}}return e||i}},b(ni,Ze);var ai=32,oi=7;function si(t,e,i,r){var n=e+1;if(n===i)return 1;if(r(t[n++],t[e])<0){for(;n<i&&r(t[n],t[n-1])<0;)n++;!function(t,e,i){i--;for(;e<i;){var r=t[e];t[e++]=t[i],t[i--]=r}}(t,e,n)}else for(;n<i&&0<=r(t[n],t[n-1]);)n++;return n-e}function hi(t,e,i,r,n){for(r===e&&r++;r<i;r++){for(var a,o=t[r],s=e,h=r;s<h;)n(o,t[a=s+h>>>1])<0?h=a:s=1+a;var l=r-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<l;)t[s+l]=t[s+l-1],l--}t[s]=o}}function li(t,e,i,r,n,a){var o=0,s=0,h=1;if(0<a(t,e[i+n])){for(s=r-n;h<s&&0<a(t,e[i+n+h]);)(h=1+((o=h)<<1))<=0&&(h=s);s<h&&(h=s),o+=n,h+=n}else{for(s=n+1;h<s&&a(t,e[i+n-h])<=0;)(h=1+((o=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=o;o=n-h,h=n-l}for(o++;o<h;){var u=o+(h-o>>>1);0<a(t,e[i+u])?o=u+1:h=u}return h}function ui(t,e,i,r,n,a){var o=0,s=0,h=1;if(a(t,e[i+n])<0){for(s=n+1;h<s&&a(t,e[i+n-h])<0;)(h=1+((o=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=o;o=n-h,h=n-l}else{for(s=r-n;h<s&&0<=a(t,e[i+n+h]);)(h=1+((o=h)<<1))<=0&&(h=s);s<h&&(h=s),o+=n,h+=n}for(o++;o<h;){var u=o+(h-o>>>1);a(t,e[i+u])<0?h=u:o=u+1}return h}function ci(p,g){var o,s,v=oi,h=0,_=[];function e(t){var e=o[t],i=s[t],r=o[t+1],n=s[t+1];s[t]=i+n,t===h-3&&(o[t+1]=o[t+2],s[t+1]=s[t+2]),h--;var a=ui(p[r],p,e,i,0,g);e+=a,0!==(i-=a)&&0!==(n=li(p[e+i-1],p,r,n,n-1,g))&&(i<=n?function(t,e,i,r){var n=0;for(n=0;n<e;n++)_[n]=p[t+n];var a=0,o=i,s=t;if(p[s++]=p[o++],0==--r){for(n=0;n<e;n++)p[s+n]=_[a+n];return}if(1===e){for(n=0;n<r;n++)p[s+n]=p[o+n];return p[s+r]=_[a]}var h,l,u,c=v;for(;;){l=h=0,u=!1;do{if(g(p[o],_[a])<0){if(p[s++]=p[o++],l++,(h=0)==--r){u=!0;break}}else if(p[s++]=_[a++],h++,l=0,1==--e){u=!0;break}}while((h|l)<c);if(u)break;do{if(0!==(h=ui(p[o],_,a,e,0,g))){for(n=0;n<h;n++)p[s+n]=_[a+n];if(s+=h,a+=h,(e-=h)<=1){u=!0;break}}if(p[s++]=p[o++],0==--r){u=!0;break}if(0!==(l=li(_[a],p,o,r,0,g))){for(n=0;n<l;n++)p[s+n]=p[o+n];if(s+=l,o+=l,0===(r-=l)){u=!0;break}}if(p[s++]=_[a++],1==--e){u=!0;break}c--}while(oi<=h||oi<=l);if(u)break;c<0&&(c=0),c+=2}if((v=c)<1&&(v=1),1===e){for(n=0;n<r;n++)p[s+n]=p[o+n];p[s+r]=_[a]}else{if(0===e)throw new Error;for(n=0;n<e;n++)p[s+n]=_[a+n]}}(e,i,r,n):function(t,e,i,r){var n=0;for(n=0;n<r;n++)_[n]=p[i+n];var a=t+e-1,o=r-1,s=i+r-1,h=0,l=0;if(p[s--]=p[a--],0==--e){for(h=s-(r-1),n=0;n<r;n++)p[h+n]=_[n];return}if(1===r){for(l=(s-=e)+1,h=(a-=e)+1,n=e-1;0<=n;n--)p[l+n]=p[h+n];return p[s]=_[o]}var u=v;for(;;){var c=0,f=0,d=!1;do{if(g(_[o],p[a])<0){if(p[s--]=p[a--],c++,(f=0)==--e){d=!0;break}}else if(p[s--]=_[o--],f++,c=0,1==--r){d=!0;break}}while((c|f)<u);if(d)break;do{if(0!==(c=e-ui(_[o],p,t,e,e-1,g))){for(e-=c,l=(s-=c)+1,h=(a-=c)+1,n=c-1;0<=n;n--)p[l+n]=p[h+n];if(0===e){d=!0;break}}if(p[s--]=_[o--],1==--r){d=!0;break}if(0!==(f=r-li(p[a],_,0,r,r-1,g))){for(r-=f,l=(s-=f)+1,h=(o-=f)+1,n=0;n<f;n++)p[l+n]=_[h+n];if(r<=1){d=!0;break}}if(p[s--]=p[a--],0==--e){d=!0;break}u--}while(oi<=c||oi<=f);if(d)break;u<0&&(u=0),u+=2}(v=u)<1&&(v=1);if(1===r){for(l=(s-=e)+1,h=(a-=e)+1,n=e-1;0<=n;n--)p[l+n]=p[h+n];p[s]=_[o]}else{if(0===r)throw new Error;for(h=s-(r-1),n=0;n<r;n++)p[h+n]=_[n]}}(e,i,r,n))}o=[],s=[],this.mergeRuns=function(){for(;1<h;){var t=h-2;if(1<=t&&s[t-1]<=s[t]+s[t+1]||2<=t&&s[t-2]<=s[t]+s[t-1])s[t-1]<s[t+1]&&t--;else if(s[t]>s[t+1])break;e(t)}},this.forceMergeRuns=function(){for(;1<h;){var t=h-2;0<t&&s[t-1]<s[t+1]&&t--,e(t)}},this.pushRun=function(t,e){o[h]=t,s[h]=e,h+=1}}function fi(t,e,i,r){i=i||0;var n=(r=r||t.length)-i;if(!(n<2)){var a=0;if(n<ai)hi(t,i,r,i+(a=si(t,i,r,e)),e);else{var o=new ci(t,e),s=function(t){for(var e=0;ai<=t;)e|=1&t,t>>=1;return t+e}(n);do{if((a=si(t,i,r,e))<s){var h=n;s<h&&(h=s),hi(t,i,i+h,i+a,e),a=h}o.pushRun(i,a),o.mergeRuns(),n-=a,i+=a}while(0!==n);o.forceMergeRuns()}}}function di(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function pi(){this._roots=[],this._displayList=[],this._displayListLen=0}pi.prototype={constructor:pi,traverse:function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,r=0,n=e.length;r<n;r++)this._updateAndAddDisplayable(e[r],null,t);i.length=this._displayListLen,_.canvasSupported&&fi(i,di)},_updateAndAddDisplayable:function(t,e,i){if(!t.ignore||i){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var r=t.clipPath;if(r){e=e?e.slice():[];for(var n=r,a=t;n;)n.parent=a,n.updateTransform(),e.push(n),n=(a=n).clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var h=o[s];t.__dirty&&(h.__dirty=!0),this._updateAndAddDisplayable(h,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof ni&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var i=this._roots[e];i instanceof ni&&i.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array){e=0;for(var r=t.length;e<r;e++)this.delRoot(t[e])}else{var n=w(this._roots,t);0<=n&&(this.delFromStorage(t),this._roots.splice(n,1),t instanceof ni&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:di};var gi={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},vi=function(t,e,i){return gi.hasOwnProperty(e)?i*t.dpr:i},_i={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},mi=9,yi=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],xi=function(t){this.extendFrom(t,!1)};function wi(t,e,i){var r=null==e.x?0:e.x,n=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;return e.global||(r=r*i.width+i.x,n=n*i.width+i.x,a=a*i.height+i.y,o=o*i.height+i.y),r=isNaN(r)?0:r,n=isNaN(n)?1:n,a=isNaN(a)?0:a,o=isNaN(o)?0:o,t.createLinearGradient(r,a,n,o)}function bi(t,e,i){var r=i.width,n=i.height,a=Math.min(r,n),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,h=null==e.r?.5:e.r;return e.global||(o=o*r+i.x,s=s*n+i.y,h*=a),t.createRadialGradient(o,s,0,o,s,h)}xi.prototype={constructor:xi,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,i){var r=this,n=i&&i.style,a=!n||t.__attrCachedBy!==_i.STYLE_BIND;t.__attrCachedBy=_i.STYLE_BIND;for(var o=0;o<yi.length;o++){var s=yi[o],h=s[0];!a&&r[h]===n[h]||(t[h]=vi(t,h,r[h]||s[1]))}if(!a&&r.fill===n.fill||(t.fillStyle=r.fill),!a&&r.stroke===n.stroke||(t.strokeStyle=r.stroke),!a&&r.opacity===n.opacity||(t.globalAlpha=null==r.opacity?1:r.opacity),!a&&r.blend===n.blend||(t.globalCompositeOperation=r.blend||"source-over"),this.hasStroke()){var l=r.lineWidth;t.lineWidth=l/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&0<this.lineWidth},extendFrom:function(t,e){if(t)for(var i in t)!t.hasOwnProperty(i)||!0!==e&&(!1===e?this.hasOwnProperty(i):null==t[i])||(this[i]=t[i])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var r=("radial"===e.type?bi:wi)(t,e,i),n=e.colorStops,a=0;a<n.length;a++)r.addColorStop(n[a].offset,n[a].color);return r}};for(var ki=xi.prototype,Ti=0;Ti<yi.length;Ti++){var Si=yi[Ti];Si[0]in ki||(ki[Si[0]]=Si[1])}xi.getGradient=ki.getGradient;function Ci(t,e){this.image=t,this.repeat=e,this.type="pattern"}function Mi(){return!1}function Ai(t,e,i){var r=m(),n=e.getWidth(),a=e.getHeight(),o=r.style;return o&&(o.position="absolute",o.left=0,o.top=0,o.width=n+"px",o.height=a+"px",r.setAttribute("data-zr-dom-id",t)),r.width=n*i,r.height=a*i,r}function Pi(t,e,i){var r;i=i||Xe,"string"==typeof t?r=Ai(t,e,i):L(t)&&(t=(r=t).id),this.id=t;var n=(this.dom=r).style;n&&(r.onselectstart=Mi,n["-webkit-user-select"]="none",n["user-select"]="none",n["-webkit-touch-callout"]="none",n["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",n.padding=0,n.margin=0,n["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=i}Pi.prototype={constructor:Pi,__dirty:!0,__used:!(Ci.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")}),__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=Ai("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,r=this.dom,n=r.style,a=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),r.width=t*i,r.height=e*i,a&&(a.width=t*i,a.height=e*i,1!==i&&this.ctxBack.scale(i,i))},clear:function(t,e){var i,r=this.dom,n=this.ctx,a=r.width,o=r.height,s=(e=e||this.clearColor,this.motionBlur&&!t),h=this.lastFrameAlpha,l=this.dpr;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(r,0,0,a/l,o/l)),n.clearRect(0,0,a,o),e&&"transparent"!==e&&(e.colorStops?(i=e.__canvasGradient||xi.getGradient(n,e,{x:0,y:0,width:a,height:o}),e.__canvasGradient=i):e.image&&(i=Ci.prototype.getCanvasPattern.call(e,n)),n.save(),n.fillStyle=i||e,n.fillRect(0,0,a,o),n.restore());if(s){var u=this.domBack;n.save(),n.globalAlpha=h,n.drawImage(u,0,0,a,o),n.restore()}}};var Li="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},zi=new ne(50);function Di(t){if("string"!=typeof t)return t;var e=zi.get(t);return e&&e.image}function Bi(t,e,i,r,n){if(t){if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!i)return e;var a=zi.get(t),o={hostEl:i,cb:r,cbPayload:n};return a?Oi(e=a.image)||a.pending.push(o):((e=new Image).onload=e.onerror=Ii,zi.put(t,e.__cachedImgObj={image:e,pending:[o]}),e.src=e.__zrImageSrc=t),e}return e}function Ii(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var i=t.pending[e],r=i.cb;r&&r(this,i.cbPayload),i.hostEl.dirty()}t.pending.length=0}function Oi(t){return t&&t.width&&t.height}var Ri={},Ei=0,Fi=5e3,Hi=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,Ni="12px sans-serif",Wi={};function Vi(t,e){var i=t+":"+(e=e||Ni);if(Ri[i])return Ri[i];for(var r,n,a=(t+"").split("\n"),o=0,s=0,h=a.length;s<h;s++)o=Math.max((r=a[s],n=e,Wi.measureText(r,n)).width,o);return Fi<Ei&&(Ei=0,Ri={}),Ei++,Ri[i]=o}function Xi(t,e,i,r,n,a,o,s){return o?function(t,e,i,r,n,a,o,s){var h=Ji(t,{rich:o,truncate:s,font:e,textAlign:i,textPadding:n,textLineHeight:a}),l=h.outerWidth,u=h.outerHeight,c=qi(0,l,i),f=ji(0,u,r);return new ri(c,f,l,u)}(t,e,i,r,n,a,o,s):function(t,e,i,r,n,a,o){var s=Ki(t,e,n,a,o),h=Vi(t,e);n&&(h+=n[1]+n[3]);var l=s.outerHeight,u=qi(0,h,i),c=ji(0,l,r),f=new ri(u,c,h,l);return f.lineHeight=s.lineHeight,f}(t,e,i,r,n,a,s)}function qi(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function ji(t,e,i){return"middle"===i?t-=e/2:"bottom"===i&&(t-=e),t}function Yi(t,e,i){var r=e.textPosition,n=e.textDistance,a=i.x,o=i.y;n=n||0;var s=i.height,h=i.width,l=s/2,u="left",c="top";switch(r){case"left":a-=n,o+=l,u="right",c="middle";break;case"right":a+=n+h,o+=l,c="middle";break;case"top":a+=h/2,o-=n,u="center",c="bottom";break;case"bottom":a+=h/2,o+=s+n,u="center";break;case"inside":a+=h/2,o+=l,u="center",c="middle";break;case"insideLeft":a+=n,o+=l,c="middle";break;case"insideRight":a+=h-n,o+=l,u="right",c="middle";break;case"insideTop":a+=h/2,o+=n,u="center";break;case"insideBottom":a+=h/2,o+=s-n,u="center",c="bottom";break;case"insideTopLeft":a+=n,o+=n;break;case"insideTopRight":a+=h-n,o+=n,u="right";break;case"insideBottomLeft":a+=n,o+=s-n,c="bottom";break;case"insideBottomRight":a+=h-n,o+=s-n,u="right",c="bottom"}return(t=t||{}).x=a,t.y=o,t.textAlign=u,t.textVerticalAlign=c,t}function Ui(t,e,i,r,n){if(!e)return"";var a=(t+"").split("\n");n=Gi(e,i,r,n);for(var o=0,s=a.length;o<s;o++)a[o]=Zi(a[o],n);return a.join("\n")}function Gi(t,e,i,r){(r=g({},r)).font=e;i=I(i,"...");r.maxIterations=I(r.maxIterations,2);var n=r.minChar=I(r.minChar,0);r.cnCharWidth=Vi("国",e);var a=r.ascCharWidth=Vi("a",e);r.placeholder=I(r.placeholder,"");for(var o=t=Math.max(0,t-1),s=0;s<n&&a<=o;s++)o-=a;var h=Vi(i,e);return o<h&&(i="",h=0),o=t-h,r.ellipsis=i,r.ellipsisWidth=h,r.contentWidth=o,r.containerWidth=t,r}function Zi(t,e){var i=e.containerWidth,r=e.font,n=e.contentWidth;if(!i)return"";var a=Vi(t,r);if(a<=i)return t;for(var o=0;;o++){if(a<=n||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?Qi(t,n,e.ascCharWidth,e.cnCharWidth):0<a?Math.floor(t.length*n/a):0;a=Vi(t=t.substr(0,s),r)}return""===t&&(t=e.placeholder),t}function Qi(t,e,i,r){for(var n=0,a=0,o=t.length;a<o&&n<e;a++){var s=t.charCodeAt(a);n+=0<=s&&s<=127?i:r}return a}function $i(t){return Vi("国",t)}function Ki(t,e,i,r,n){null!=t&&(t+="");var a=I(r,$i(e)),o=t?t.split("\n"):[],s=o.length*a,h=s,l=!0;if(i&&(h+=i[0]+i[2]),t&&n){l=!1;var u=n.outerHeight,c=n.outerWidth;if(null!=u&&u<h)t="",o=[];else if(null!=c)for(var f=Gi(c-(i?i[1]+i[3]:0),e,n.ellipsis,{minChar:n.minChar,placeholder:n.placeholder}),d=0,p=o.length;d<p;d++)o[d]=Zi(o[d],f)}return{lines:o,height:s,outerHeight:h,lineHeight:a,canCacheByTextString:l}}function Ji(t,e){var i={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return i;for(var r,n=Hi.lastIndex=0;null!=(r=Hi.exec(t));){var a=r.index;n<a&&tr(i,t.substring(n,a)),tr(i,r[2],r[1]),n=Hi.lastIndex}n<t.length&&tr(i,t.substring(n,t.length));var o=i.lines,s=0,h=0,l=[],u=e.textPadding,c=e.truncate,f=c&&c.outerWidth,d=c&&c.outerHeight;u&&(null!=f&&(f-=u[1]+u[3]),null!=d&&(d-=u[0]+u[2]));for(var p=0;p<o.length;p++){for(var g=o[p],v=0,_=0,m=0;m<g.tokens.length;m++){var y=(P=g.tokens[m]).styleName&&e.rich[P.styleName]||{},x=P.textPadding=y.textPadding,w=P.font=y.font||e.font,b=P.textHeight=I(y.textHeight,$i(w));if(x&&(b+=x[0]+x[2]),P.height=b,P.lineHeight=O(y.textLineHeight,e.textLineHeight,b),P.textAlign=y&&y.textAlign||e.textAlign,P.textVerticalAlign=y&&y.textVerticalAlign||"middle",null!=d&&s+P.lineHeight>d)return{lines:[],width:0,height:0};P.textWidth=Vi(P.text,w);var k=y.textWidth,T=null==k||"auto"===k;if("string"==typeof k&&"%"===k.charAt(k.length-1))P.percentWidth=k,l.push(P),k=0;else{if(T){k=P.textWidth;var S=y.textBackgroundColor,C=S&&S.image;C&&Oi(C=Di(C))&&(k=Math.max(k,C.width*b/C.height))}var M=x?x[1]+x[3]:0;k+=M;var A=null!=f?f-_:null;null!=A&&A<k&&(!T||A<M?(P.text="",P.textWidth=k=0):(P.text=Ui(P.text,A-M,w,c.ellipsis,{minChar:c.minChar}),P.textWidth=Vi(P.text,w),k=P.textWidth+M))}_+=P.width=k,y&&(v=Math.max(v,P.lineHeight))}g.width=_,s+=g.lineHeight=v,h=Math.max(h,_)}i.outerWidth=i.width=I(e.textWidth,h),i.outerHeight=i.height=I(e.textHeight,s),u&&(i.outerWidth+=u[1]+u[3],i.outerHeight+=u[0]+u[2]);for(p=0;p<l.length;p++){var P,L=(P=l[p]).percentWidth;P.width=parseInt(L,10)/100*h}return i}function tr(t,e,i){for(var r=""===e,n=e.split("\n"),a=t.lines,o=0;o<n.length;o++){var s=n[o],h={styleName:i,text:s,isLineHolder:!s&&!r};if(o)a.push({tokens:[h]});else{var l=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=l.length;1===u&&l[0].isLineHolder?l[0]=h:!s&&u&&!r||l.push(h)}}}function er(t,e){var i,r,n,a,o,s=e.x,h=e.y,l=e.width,u=e.height,c=e.r;l<0&&(s+=l,l=-l),u<0&&(h+=u,u=-u),"number"==typeof c?i=r=n=a=c:c instanceof Array?1===c.length?i=r=n=a=c[0]:2===c.length?(i=n=c[0],r=a=c[1]):3===c.length?(i=c[0],r=a=c[1],n=c[2]):(i=c[0],r=c[1],n=c[2],a=c[3]):i=r=n=a=0,l<i+r&&(i*=l/(o=i+r),r*=l/o),l<n+a&&(n*=l/(o=n+a),a*=l/o),u<r+n&&(r*=u/(o=r+n),n*=u/o),u<i+a&&(i*=u/(o=i+a),a*=u/o),t.moveTo(s+i,h),t.lineTo(s+l-r,h),0!==r&&t.arc(s+l-r,h+r,r,-Math.PI/2,0),t.lineTo(s+l,h+u-n),0!==n&&t.arc(s+l-n,h+u-n,n,0,Math.PI/2),t.lineTo(s+a,h+u),0!==a&&t.arc(s+a,h+u-a,a,Math.PI/2,Math.PI),t.lineTo(s,h+i),0!==i&&t.arc(s+i,h+i,i,Math.PI,1.5*Math.PI)}Wi.measureText=function(t,e){var i=x();return i.font=e||Ni,i.measureText(t)};var ir=Ni,rr={left:1,right:1,center:1},nr={top:1,bottom:1,middle:1},ar=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],or={},sr={};function hr(t){return lr(t),T(t.rich,lr),t}function lr(t){if(t){t.font=function(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&E(e)||t.textFont||t.font}(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||rr[e]?e:"left";var i=t.textVerticalAlign||t.textBaseline;"center"===i&&(i="middle"),t.textVerticalAlign=null==i||nr[i]?i:"top",t.textPadding&&(t.textPadding=R(t.textPadding))}}function ur(t,e,i,r,n,a){r.rich?function(t,e,i,r,n,a){a!==mi&&(e.__attrCachedBy=_i.NONE);var o=t.__textCotentBlock;o&&!t.__dirtyText||(o=t.__textCotentBlock=Ji(i,r));!function(t,e,i,r,n){var a=i.width,o=i.outerWidth,s=i.outerHeight,h=r.textPadding,l=vr(sr,t,r,n),u=l.baseX,c=l.baseY,f=l.textAlign,d=l.textVerticalAlign;cr(e,r,n,u,c);var p=qi(u,o,f),g=ji(c,s,d),v=p,_=g;h&&(v+=h[3],_+=h[0]);var m=v+a;dr(r)&&pr(t,e,r,p,g,o,s);for(var y=0;y<i.lines.length;y++){for(var x,w=i.lines[y],b=w.tokens,k=b.length,T=w.lineHeight,S=w.width,C=0,M=v,A=m,P=k-1;C<k&&(!(x=b[C]).textAlign||"left"===x.textAlign);)fr(t,e,x,r,T,_,M,"left"),S-=x.width,M+=x.width,C++;for(;0<=P&&"right"===(x=b[P]).textAlign;)fr(t,e,x,r,T,_,A,"right"),S-=x.width,A-=x.width,P--;for(M+=(a-(M-v)-(m-A)-S)/2;C<=P;)x=b[C],fr(t,e,x,r,T,_,M+x.width/2,"center"),M+=x.width,C++;_+=T}}(t,e,o,r,n)}(t,e,i,r,n,a):function(t,e,i,r,n,a){var o,s=dr(r),h=!1,l=e.__attrCachedBy===_i.PLAIN_TEXT;a!==mi?(a&&(o=a.style,h=!s&&l&&o),e.__attrCachedBy=s?_i.NONE:_i.PLAIN_TEXT):l&&(e.__attrCachedBy=_i.NONE);var u=r.font||ir;h&&u===(o.font||ir)||(e.font=u);var c=t.__computedFont;t.__styleFont!==u&&(t.__styleFont=u,c=t.__computedFont=e.font);var f=r.textPadding,d=r.textLineHeight,p=t.__textCotentBlock;p&&!t.__dirtyText||(p=t.__textCotentBlock=Ki(i,c,f,d,r.truncate));var g=p.outerHeight,v=p.lines,_=p.lineHeight,m=vr(sr,t,r,n),y=m.baseX,x=m.baseY,w=m.textAlign||"left",b=m.textVerticalAlign;cr(e,r,n,y,x);var k=ji(x,g,b),T=y,S=k;if(s||f){var C=Vi(i,c);f&&(C+=f[1]+f[3]);var M=qi(y,C,w);s&&pr(t,e,r,M,k,C,g),f&&(T=wr(y,w,f),S+=f[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=r.opacity||1;for(var A=0;A<ar.length;A++){var P=ar[A],L=P[0],z=P[1],D=r[L];h&&D===o[L]||(e[z]=vi(e,z,D||P[2]))}S+=_/2;var B=r.textStrokeWidth,I=h?o.textStrokeWidth:null,O=!h||B!==I,R=!h||O||r.textStroke!==o.textStroke,E=mr(r.textStroke,B),F=yr(r.textFill);E&&(O&&(e.lineWidth=B),R&&(e.strokeStyle=E));F&&(h&&r.textFill===o.textFill||(e.fillStyle=F));if(1===v.length)E&&e.strokeText(v[0],T,S),F&&e.fillText(v[0],T,S);else for(A=0;A<v.length;A++)E&&e.strokeText(v[A],T,S),F&&e.fillText(v[A],T,S),S+=_}(t,e,i,r,n,a)}function cr(t,e,i,r,n){if(i&&e.textRotation){var a=e.textOrigin;"center"===a?(r=i.width/2+i.x,n=i.height/2+i.y):a&&(r=a[0]+i.x,n=a[1]+i.y),t.translate(r,n),t.rotate(-e.textRotation),t.translate(-r,-n)}}function fr(t,e,i,r,n,a,o,s){var h=r.rich[i.styleName]||{};h.text=i.text;var l=i.textVerticalAlign,u=a+n/2;"top"===l?u=a+i.height/2:"bottom"===l&&(u=a+n-i.height/2),!i.isLineHolder&&dr(h)&&pr(t,e,h,"right"===s?o-i.width:"center"===s?o-i.width/2:o,u-i.height/2,i.width,i.height);var c=i.textPadding;c&&(o=wr(o,s,c),u-=i.height/2-c[2]-i.textHeight/2),_r(e,"shadowBlur",O(h.textShadowBlur,r.textShadowBlur,0)),_r(e,"shadowColor",h.textShadowColor||r.textShadowColor||"transparent"),_r(e,"shadowOffsetX",O(h.textShadowOffsetX,r.textShadowOffsetX,0)),_r(e,"shadowOffsetY",O(h.textShadowOffsetY,r.textShadowOffsetY,0)),_r(e,"textAlign",s),_r(e,"textBaseline","middle"),_r(e,"font",i.font||ir);var f=mr(h.textStroke||r.textStroke,p),d=yr(h.textFill||r.textFill),p=I(h.textStrokeWidth,r.textStrokeWidth);f&&(_r(e,"lineWidth",p),_r(e,"strokeStyle",f),e.strokeText(i.text,o,u)),d&&(_r(e,"fillStyle",d),e.fillText(i.text,o,u))}function dr(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function pr(t,e,i,r,n,a,o){var s=i.textBackgroundColor,h=i.textBorderWidth,l=i.textBorderColor,u=P(s);if(_r(e,"shadowBlur",i.textBoxShadowBlur||0),_r(e,"shadowColor",i.textBoxShadowColor||"transparent"),_r(e,"shadowOffsetX",i.textBoxShadowOffsetX||0),_r(e,"shadowOffsetY",i.textBoxShadowOffsetY||0),u||h&&l){e.beginPath();var c=i.textBorderRadius;c?er(e,{x:r,y:n,width:a,height:o,r:c}):e.rect(r,n,a,o),e.closePath()}if(u)if(_r(e,"fillStyle",s),null!=i.fillOpacity){var f=e.globalAlpha;e.globalAlpha=i.fillOpacity*i.opacity,e.fill(),e.globalAlpha=f}else e.fill();else if(L(s)){var d=s.image;(d=Bi(d,null,t,gr,s))&&Oi(d)&&e.drawImage(d,r,n,a,o)}if(h&&l)if(_r(e,"lineWidth",h),_r(e,"strokeStyle",l),null!=i.strokeOpacity){f=e.globalAlpha;e.globalAlpha=i.strokeOpacity*i.opacity,e.stroke(),e.globalAlpha=f}else e.stroke()}function gr(t,e){e.image=t}function vr(t,e,i,r){var n=i.x||0,a=i.y||0,o=i.textAlign,s=i.textVerticalAlign;if(r){var h=i.textPosition;if(h instanceof Array)n=r.x+xr(h[0],r.width),a=r.y+xr(h[1],r.height);else{var l=e&&e.calculateTextPosition?e.calculateTextPosition(or,i,r):Yi(or,i,r);n=l.x,a=l.y,o=o||l.textAlign,s=s||l.textVerticalAlign}var u=i.textOffset;u&&(n+=u[0],a+=u[1])}return(t=t||{}).baseX=n,t.baseY=a,t.textAlign=o,t.textVerticalAlign=s,t}function _r(t,e,i){return t[e]=vi(t,e,i),t[e]}function mr(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function yr(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function xr(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function wr(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}function br(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function kr(){}var Tr=new ri;function Sr(t){for(var e in t=t||{},Ze.call(this,t),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new xi(t.style,this),this._rect=null,this.__clipPaths=null}function Cr(t){Sr.call(this,t)}Sr.prototype={constructor:Sr,type:"displayable",__dirty:!0,invisible:!(kr.prototype={constructor:kr,drawRectText:function(t,e){var i=this.style;e=i.textRect||e,this.__dirty&&hr(i);var r=i.text;if(null!=r&&(r+=""),br(r,i)){t.save();var n=this.transform;i.transformText?this.setTransform(t):n&&(Tr.copy(e),Tr.applyTransform(n),e=Tr),ur(this,t,r,i,e,mi),t.restore()}}}),z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(i[0],i[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?Ze.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new xi(t,this),this.dirty(!1),this},calculateTextPosition:null},b(Sr,Ze),k(Sr,kr),Cr.prototype={constructor:Cr,type:"image",brush:function(t,e){var i=this.style,r=i.image;i.bind(t,this,e);var n=this._image=Bi(r,this._image,this,this.onload);if(n&&Oi(n)){var a=i.x||0,o=i.y||0,s=i.width,h=i.height,l=n.width/n.height;if(null==s&&null!=h?s=h*l:null==h&&null!=s?h=s/l:null==s&&null==h&&(s=n.width,h=n.height),this.setTransform(t),i.sWidth&&i.sHeight){var u=i.sx||0,c=i.sy||0;t.drawImage(n,u,c,i.sWidth,i.sHeight,a,o,s,h)}else if(i.sx&&i.sy){var f=s-(u=i.sx),d=h-(c=i.sy);t.drawImage(n,u,c,f,d,a,o,s,h)}else t.drawImage(n,a,o,s,h);null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new ri(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},b(Cr,Sr);var Mr=314159;function Ar(t){return parseInt(t,10)}var Pr=new ri(0,0,0,0),Lr=new ri(0,0,0,0);function zr(t,e,i){this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=g({},i||{}),this.dpr=i.devicePixelRatio||Xe,this._singleCanvas=r;var n=(this.root=t).style;n&&(n["-webkit-tap-highlight-color"]="transparent",n["-webkit-user-select"]=n["user-select"]=n["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],o=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,r){var s=t.width,h=t.height;null!=i.width&&(s=i.width),null!=i.height&&(h=i.height),this.dpr=i.devicePixelRatio||1,t.width=s*this.dpr,t.height=h*this.dpr,this._width=s,this._height=h;var l=new Pi(t,this,this.dpr);l.__builtin__=!0,l.initContext(),(o[Mr]=l).zlevel=Mr,a.push(Mr),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var u=this._domRoot=function(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}(this._width,this._height);t.appendChild(u)}this._hoverlayer=null,this._hoverElements=[]}zr.prototype={constructor:zr,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var r=0;r<i.length;r++){var n=i[r],a=this._layers[n];if(!a.__builtin__&&a.refresh){var o=0===r?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return(i.__from=t).__hoverMir=i,e&&i.setStyle(e),this._hoverElements.push(i),i}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,r=w(i,e);0<=r&&i.splice(r,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,i=0;i<e.length;i++){var r=e[i].__from;r&&(r.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,i=this._hoverlayer;if(i&&i.clear(),e){fi(t,this.storage.displayableSortFunc);var r={};(i=i||(this._hoverlayer=this.getLayer(1e5))).ctx.save();for(var n=0;n<e;){var a=t[n],o=a.__from;o&&o.__zr?(n++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,i,!0,r))):(t.splice(n,1),o.__hoverMir=null,e--)}i.ctx.restore()}},getHoverLayer:function(){return this.getLayer(1e5)},_paintList:function(t,e,i){if(this._redrawId===i){e=e||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!r){var n=this;Li(function(){n._paintList(t,e,i)})}}},_compositeManually:function(){var e=this.getLayer(Mr).ctx,i=this._domRoot.width,r=this._domRoot.height;e.clearRect(0,0,i,r),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,i,r)})},_doPaintList:function(t,e){for(var i=[],r=0;r<this._zlevelList.length;r++){var n=this._zlevelList[r];(s=this._layers[n]).__builtin__&&s!==this._hoverlayer&&(s.__dirty||e)&&i.push(s)}for(var a=!0,o=0;o<i.length;o++){var s,h=(s=i[o]).ctx,l={};h.save();var u=e?s.__startIndex:s.__drawIndex,c=!e&&s.incremental&&Date.now,f=c&&Date.now(),d=s.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(s.__startIndex===s.__endIndex)s.clear(!1,d);else if(u===s.__startIndex){var p=t[u];p.incremental&&p.notClear&&!e||s.clear(!1,d)}-1===u&&(console.error("For some unknown reason. drawIndex is -1"),u=s.__startIndex);for(var g=u;g<s.__endIndex;g++){var v=t[g];if(this._doPaintEl(v,s,e,l),v.__dirty=v.__dirtyText=!1,c)if(15<Date.now()-f)break}s.__drawIndex=g,s.__drawIndex<s.__endIndex&&(a=!1),l.prevElClipPaths&&h.restore(),h.restore()}return _.wxa&&T(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),a},_doPaintEl:function(t,e,i,r){var n=e.ctx,a=t.transform;if((e.__dirty||i)&&!t.invisible&&0!==t.style.opacity&&(!a||a[0]||a[3])&&(!t.culling||!function(t,e,i){return Pr.copy(t.getBoundingRect()),t.transform&&Pr.applyTransform(t.transform),Lr.width=e,Lr.height=i,!Pr.intersect(Lr)}(t,this._width,this._height))){var o=t.__clipPaths,s=r.prevElClipPaths;s&&!function(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var i=0;i<t.length;i++)if(t[i]!==e[i])return!0;return!1}(o,s)||(s&&(n.restore(),r.prevElClipPaths=null,r.prevEl=null),o&&(n.save(),function(t,e){for(var i=0;i<t.length;i++){var r=t[i];r.setTransform(e),e.beginPath(),r.buildPath(e,r.shape),e.clip(),r.restoreTransform(e)}}(o,n),r.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(n),t.brush(n,r.prevEl||null),(r.prevEl=t).afterBrush&&t.afterBrush(n)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Mr);var i=this._layers[t];return i||((i=new Pi("zr_"+t,this,this.dpr)).zlevel=t,i.__builtin__=!0,this._layerConfig[t]?p(i,this._layerConfig[t],!0):this._layerConfig[t-.01]&&p(i,this._layerConfig[t-.01],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},insertLayer:function(t,e){var i=this._layers,r=this._zlevelList,n=r.length,a=null,o=-1,s=this._domRoot;if(i[t])Ye("ZLevel "+t+" has been used already");else if(function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(0<n&&t>r[0]){for(o=0;o<n-1&&!(r[o]<t&&r[o+1]>t);o++);a=i[r[o]]}if(r.splice(o+1,0,t),!(i[t]=e).virtual)if(a){var h=a.dom;h.nextSibling?s.insertBefore(e.dom,h.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)}else Ye("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){var i,r,n=this._zlevelList;for(r=0;r<n.length;r++)i=n[r],t.call(e,this._layers[i],i)},eachBuiltinLayer:function(t,e){var i,r,n,a=this._zlevelList;for(n=0;n<a.length;n++)r=a[n],(i=this._layers[r]).__builtin__&&t.call(e,i,r)},eachOtherLayer:function(t,e){var i,r,n,a=this._zlevelList;for(n=0;n<a.length;n++)r=a[n],(i=this._layers[r]).__builtin__||t.call(e,i,r)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){n&&(n.__endIndex!==t&&(n.__dirty=!0),n.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var i=1;i<t.length;i++){if((o=t[i]).zlevel!==t[i-1].zlevel||o.incremental){this._needsManuallyCompositing=!0;break}}var r,n=null,a=0;for(i=0;i<t.length;i++){var o,s,h=(o=t[i]).zlevel;r!==h&&(r=h,a=0),o.incremental?((s=this.getLayer(h+.001,this._needsManuallyCompositing)).incremental=!0,a=1):s=this.getLayer(h+(0<a?.01:0),this._needsManuallyCompositing),s.__builtin__||Ye("ZLevel "+h+" has been used by unkown layer "+s.id),s!==n&&(s.__used=!0,s.__startIndex!==i&&(s.__dirty=!0),s.__startIndex=i,s.incremental?s.__drawIndex=-1:s.__drawIndex=i,e(i),n=s),o.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=i))}e(i),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?p(i[t],e,!0):i[t]=e;for(var r=0;r<this._zlevelList.length;r++){var n=this._zlevelList[r];if(n===t||n===t+.01)p(this._layers[n],i[t],!0)}}},delLayer:function(t){var e=this._layers,i=this._zlevelList,r=e[t];r&&(r.dom.parentNode.removeChild(r.dom),delete e[t],i.splice(w(i,t),1))},resize:function(e,i){if(this._domRoot.style){var t=this._domRoot;t.style.display="none";var r=this._opts;if(null!=e&&(r.width=e),null!=i&&(r.height=i),e=this._getSize(0),i=this._getSize(1),t.style.display="",this._width!==e||i!==this._height){for(var n in t.style.width=e+"px",t.style.height=i+"px",this._layers)this._layers.hasOwnProperty(n)&&this._layers[n].resize(e,i);T(this._progressiveLayers,function(t){t.resize(e,i)}),this.refresh(!0)}this._width=e,this._height=i}else{if(null==e||null==i)return;this._width=e,this._height=i,this.getLayer(Mr).resize(e,i)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Mr].dom;var e=new Pi("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height,n=e.ctx;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var h=o[s];this._doPaintEl(h,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],r=["clientWidth","clientHeight"][t],n=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[r]||Ar(s[i])||Ar(o.style[i]))-(Ar(s[n])||0)-(Ar(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),r=i.getContext("2d"),n=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,h=a.shadowOffsetY*e,l=a.hasStroke()?a.lineWidth:0,u=Math.max(l/2,o-s),c=Math.max(l/2,s+o),f=Math.max(l/2,o-h),d=Math.max(l/2,h+o),p=n.width+u+c,g=n.height+f+d;i.width=p*e,i.height=g*e,r.scale(e,e),r.clearRect(0,0,p,g),r.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-n.x,f-n.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(r);var _=new Cr({style:{x:0,y:0,image:i}});return null!=v.position&&(_.position=t.position=v.position),null!=v.rotation&&(_.rotation=t.rotation=v.rotation),null!=v.scale&&(_.scale=t.scale=v.scale),_}};function Dr(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,ut.call(this)}Dr.prototype={constructor:Dr,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),i=0;i<e.length;i++)this.addClip(e[i])},removeClip:function(t){var e=w(this._clips,t);0<=e&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e.length;i++)this.removeClip(e[i]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,r=i.length,n=[],a=[],o=0;o<r;o++){var s=i[o],h=s.step(t,e);h&&(n.push(h),a.push(s))}for(o=0;o<r;)i[o]._needsRemove?(i[o]=i[r-1],i.pop(),r--):o++;r=n.length;for(o=0;o<r;o++)a[o].fire(n[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var e=this;this._running=!0,Li(function t(){e._running&&(Li(t),e._paused||e._update())})},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){var i=new We(t,(e=e||{}).loop,e.getter,e.setter);return this.addAnimator(i),i}},k(Dr,ut);var Br,Ir,Or=_.domSupported,Rr=(Ir={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Br=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:S(Br,function(t){var e=t.replace("mouse","pointer");return Ir.hasOwnProperty(e)?e:t})}),Er={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]};function Fr(t){return"mousewheel"===t&&_.browser.firefox?"DOMMouseScroll":t}function Hr(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Nr(t){t&&(t.zrByTouch=!0)}function Wr(t,e){for(var i=e,r=!1;i&&9!==i.nodeType&&!(r=i.domBelongToZr||i!==e&&i===t.painterRoot);)i=i.parentNode;return r}function Vr(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}var Xr=Vr.prototype;Xr.stopPropagation=Xr.stopImmediatePropagation=Xr.preventDefault=W;var qr={mousedown:function(t){t=St(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=St(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||Qr(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=St(this.dom,t),Qr(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=St(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=Wr(this,e),this.trigger("mouseout",t)},touchstart:function(t){Nr(t=St(this.dom,t)),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),qr.mousemove.call(this,t),qr.mousedown.call(this,t)},touchmove:function(t){Nr(t=St(this.dom,t)),this.handler.processGesture(t,"change"),qr.mousemove.call(this,t)},touchend:function(t){Nr(t=St(this.dom,t)),this.handler.processGesture(t,"end"),qr.mouseup.call(this,t),+new Date-this._lastTouchMoment<300&&qr.click.call(this,t)},pointerdown:function(t){qr.mousedown.call(this,t)},pointermove:function(t){Hr(t)||qr.mousemove.call(this,t)},pointerup:function(t){qr.mouseup.call(this,t)},pointerout:function(t){Hr(t)||qr.mouseout.call(this,t)}};T(["click","mousewheel","dblclick","contextmenu"],function(e){qr[e]=function(t){t=St(this.dom,t),this.trigger(e,t)}});var jr={pointermove:function(t){Hr(t)||jr.mousemove.call(this,t)},pointerup:function(t){jr.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;Qr(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function Yr(i,r){var n=r.domHandlers;_.pointerEventsSupported?T(Rr.pointer,function(e){Gr(r,e,function(t){n[e].call(i,t)})}):(_.touchEventsSupported&&T(Rr.touch,function(e){Gr(r,e,function(t){n[e].call(i,t),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}(r)})}),T(Rr.mouse,function(e){Gr(r,e,function(t){t=Tt(t),r.touching||n[e].call(i,t)})}))}function Ur(i,r){function t(e){Gr(r,e,function(t){t=Tt(t),Wr(i,t.target)||(t=function(t,e){return St(t.dom,new Vr(t,e),!0)}(i,t),r.domHandlers[e].call(i,t))},{capture:!0})}_.pointerEventsSupported?T(Er.pointer,t):_.touchEventsSupported||T(Er.mouse,t)}function Gr(t,e,i,r){t.mounted[e]=i,t.listenerOpts[e]=r,function(t,e,i,r){yt?t.addEventListener(e,i,r):t.attachEvent("on"+e,i)}(t.domTarget,Fr(e),i,r)}function Zr(t){var e,i,r,n,a=t.mounted;for(var o in a)a.hasOwnProperty(o)&&(e=t.domTarget,i=Fr(o),r=a[o],n=t.listenerOpts[o],yt?e.removeEventListener(i,r,n):e.detachEvent("on"+i,r));t.mounted={}}function Qr(t,e){if(t._mayPointerCapture=null,Or&&t._pointerCapturing^e){t._pointerCapturing=e;var i=t._globalHandlerScope;e?Ur(t,i):Zr(i)}}function $r(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function Kr(t,e){ut.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new $r(t,qr),Or&&(this._globalHandlerScope=new $r(document,jr)),this._pointerCapturing=!1,this._mayPointerCapture=null,Yr(this,this._localHandlerScope)}var Jr=Kr.prototype;Jr.dispose=function(){Zr(this._localHandlerScope),Or&&Zr(this._globalHandlerScope)},Jr.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},k(Kr,ut);var tn=!_.canvasSupported,en={canvas:zr},rn={};function nn(t,e){en[t]=e}function an(t,e,i){i=i||{},this.dom=e,this.id=t;var r=this,n=new pi,a=i.renderer;if(tn){if(!en.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");a="vml"}else a&&en[a]||(a="canvas");var o=new en[a](e,n,i,t);this.storage=n,this.painter=o;var s=_.node||_.worker?null:new Kr(o.getViewportRoot(),o.root);this.handler=new Bt(n,o,s,o.root),this.animation=new Dr({stage:{update:C(this.flush,this)}}),this.animation.start(),this._needsRefresh;var h=n.delFromStorage,l=n.addToStorage;n.delFromStorage=function(t){h.call(n,t),t&&t.removeSelfFromZr(r)},n.addToStorage=function(t){l.call(n,t),t.addSelfToZr(r)}}an.prototype={constructor:an,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var i=this.painter.addHover(t,e);return this.refreshHover(),i}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,function(t){delete rn[t]}(this.id)}};var on=Math.pow,sn=Math.sqrt,hn=1e-8,ln=1e-4,un=sn(3),cn=1/3,fn=q(),dn=q(),pn=q();function gn(t){return-hn<t&&t<hn}function vn(t){return hn<t||t<-hn}function _n(t,e,i,r,n){var a=1-n;return a*a*(a*t+3*n*e)+n*n*(n*r+3*a*i)}function mn(t,e,i,r,n){var a=1-n;return 3*(((e-t)*a+2*(i-e)*n)*a+(r-i)*n*n)}function yn(t,e,i,r,n){var a=6*i-12*e+6*t,o=9*e+3*r-3*t-9*i,s=3*e-3*t,h=0;if(gn(o)){if(vn(a))0<=(u=-s/a)&&u<=1&&(n[h++]=u)}else{var l=a*a-4*o*s;if(gn(l))n[0]=-a/(2*o);else if(0<l){var u,c=sn(l),f=(-a-c)/(2*o);0<=(u=(-a+c)/(2*o))&&u<=1&&(n[h++]=u),0<=f&&f<=1&&(n[h++]=f)}}return h}function xn(t,e,i,r,n,a){var o=(e-t)*n+t,s=(i-e)*n+e,h=(r-i)*n+i,l=(s-o)*n+o,u=(h-s)*n+s,c=(u-l)*n+l;a[0]=t,a[1]=o,a[2]=l,a[3]=c,a[4]=c,a[5]=u,a[6]=h,a[7]=r}function wn(t,e,i,r){var n=1-r;return n*(n*t+2*r*e)+r*r*i}function bn(t,e,i,r){return 2*((1-r)*(e-t)+r*(i-e))}function kn(t,e,i){var r=t+i-2*e;return 0==r?.5:(t-e)/r}function Tn(t,e,i,r,n){var a=(e-t)*r+t,o=(i-e)*r+e,s=(o-a)*r+a;n[0]=t,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=i}var Sn=Math.min,Cn=Math.max,Mn=Math.sin,An=Math.cos,Pn=2*Math.PI,Ln=q(),zn=q(),Dn=q();function Bn(t,e,i,r,n,a){n[0]=Sn(t,i),n[1]=Sn(e,r),a[0]=Cn(t,i),a[1]=Cn(e,r)}var In=[],On=[];function Rn(t,e,i,r,n,a,o,s,h,l){var u,c=yn,f=_n,d=c(t,i,n,o,In);for(h[0]=1/0,h[1]=1/0,l[0]=-1/0,l[1]=-1/0,u=0;u<d;u++){var p=f(t,i,n,o,In[u]);h[0]=Sn(p,h[0]),l[0]=Cn(p,l[0])}for(d=c(e,r,a,s,On),u=0;u<d;u++){var g=f(e,r,a,s,On[u]);h[1]=Sn(g,h[1]),l[1]=Cn(g,l[1])}h[0]=Sn(t,h[0]),l[0]=Cn(t,l[0]),h[0]=Sn(o,h[0]),l[0]=Cn(o,l[0]),h[1]=Sn(e,h[1]),l[1]=Cn(e,l[1]),h[1]=Sn(s,h[1]),l[1]=Cn(s,l[1])}function En(t,e,i,r,n,a,o,s,h){var l=at,u=ot,c=Math.abs(n-a);if(c%Pn<1e-4&&1e-4<c)return s[0]=t-i,s[1]=e-r,h[0]=t+i,void(h[1]=e+r);if(Ln[0]=An(n)*i+t,Ln[1]=Mn(n)*r+e,zn[0]=An(a)*i+t,zn[1]=Mn(a)*r+e,l(s,Ln,zn),u(h,Ln,zn),(n%=Pn)<0&&(n+=Pn),(a%=Pn)<0&&(a+=Pn),a<n&&!o?a+=Pn:n<a&&o&&(n+=Pn),o){var f=a;a=n,n=f}for(var d=0;d<a;d+=Math.PI/2)n<d&&(Dn[0]=An(d)*i+t,Dn[1]=Mn(d)*r+e,l(s,Dn,s),u(h,Dn,h))}var Fn={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Hn=[],Nn=[],Wn=[],Vn=[],Xn=Math.min,qn=Math.max,jn=Math.cos,Yn=Math.sin,Un=Math.sqrt,Gn=Math.abs,Zn="undefined"!=typeof Float32Array,Qn=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};function $n(t,e,i,r,n,a,o){if(0===n)return!1;var s=n,h=0;if(e+s<o&&r+s<o||o<e-s&&o<r-s||t+s<a&&i+s<a||a<t-s&&a<i-s)return!1;if(t===i)return Math.abs(a-t)<=s/2;var l=(h=(e-r)/(t-i))*a-o+(t*r-i*e)/(t-i);return l*l/(h*h+1)<=s/2*s/2}function Kn(t,e,i,r,n,a,o,s,h,l,u){if(0===h)return!1;var c=h;return!(e+c<u&&r+c<u&&a+c<u&&s+c<u||u<e-c&&u<r-c&&u<a-c&&u<s-c||t+c<l&&i+c<l&&n+c<l&&o+c<l||l<t-c&&l<i-c&&l<n-c&&l<o-c)&&function(t,e,i,r,n,a,o,s,h,l,u){var c,f,d,p,g,v=.005,_=1/0;fn[0]=h,fn[1]=l;for(var m=0;m<1;m+=.05)dn[0]=_n(t,i,n,o,m),dn[1]=_n(e,r,a,s,m),(p=rt(fn,dn))<_&&(c=m,_=p);_=1/0;for(var y=0;y<32&&!(v<ln);y++)f=c-v,d=c+v,dn[0]=_n(t,i,n,o,f),dn[1]=_n(e,r,a,s,f),p=rt(dn,fn),0<=f&&p<_?(c=f,_=p):(pn[0]=_n(t,i,n,o,d),pn[1]=_n(e,r,a,s,d),g=rt(pn,fn),d<=1&&g<_?(c=d,_=g):v*=.5);return u&&(u[0]=_n(t,i,n,o,c),u[1]=_n(e,r,a,s,c)),sn(_)}(t,e,i,r,n,a,o,s,l,u,null)<=c/2}function Jn(t,e,i,r,n,a,o,s,h){if(0===o)return!1;var l=o;return!(e+l<h&&r+l<h&&a+l<h||h<e-l&&h<r-l&&h<a-l||t+l<s&&i+l<s&&n+l<s||s<t-l&&s<i-l&&s<n-l)&&function(t,e,i,r,n,a,o,s,h){var l,u=.005,c=1/0;fn[0]=o,fn[1]=s;for(var f=0;f<1;f+=.05){dn[0]=wn(t,i,n,f),dn[1]=wn(e,r,a,f),(v=rt(fn,dn))<c&&(l=f,c=v)}c=1/0;for(var d=0;d<32&&!(u<ln);d++){var p=l-u,g=l+u;dn[0]=wn(t,i,n,p),dn[1]=wn(e,r,a,p);var v=rt(dn,fn);if(0<=p&&v<c)l=p,c=v;else{pn[0]=wn(t,i,n,g),pn[1]=wn(e,r,a,g);var _=rt(pn,fn);g<=1&&_<c?(l=g,c=_):u*=.5}}return h&&(h[0]=wn(t,i,n,l),h[1]=wn(e,r,a,l)),sn(c)}(t,e,i,r,n,a,s,h,null)<=l/2}Qn.prototype={constructor:Qn,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,i){i=i||0,this._ux=Gn(i/Xe/t)||0,this._uy=Gn(i/Xe/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return(this._ctx=t)&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Fn.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=Gn(t-this._xi)>this._ux||Gn(e-this._yi)>this._uy||this._len<5;return this.addData(Fn.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,r,n,a){return this.addData(Fn.C,t,e,i,r,n,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,r,n,a):this._ctx.bezierCurveTo(t,e,i,r,n,a)),this._xi=n,this._yi=a,this},quadraticCurveTo:function(t,e,i,r){return this.addData(Fn.Q,t,e,i,r),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,r):this._ctx.quadraticCurveTo(t,e,i,r)),this._xi=i,this._yi=r,this},arc:function(t,e,i,r,n,a){return this.addData(Fn.A,t,e,i,i,r,n-r,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,r,n,a),this._xi=jn(n)*i+t,this._yi=Yn(n)*i+e,this},arcTo:function(t,e,i,r,n){return this._ctx&&this._ctx.arcTo(t,e,i,r,n),this},rect:function(t,e,i,r){return this._ctx&&this._ctx.rect(t,e,i,r),this.addData(Fn.R,t,e,i,r),this},closePath:function(){this.addData(Fn.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t;for(var e=this._dashIdx=0,i=0;i<t.length;i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Zn||(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,r=this._len,n=0;n<e;n++)i+=t[n].len();Zn&&this.data instanceof Float32Array&&(this.data=new Float32Array(r+i));for(n=0;n<e;n++)for(var a=t[n].data,o=0;o<a.length;o++)this.data[r++]=a[o];this._len=r},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var i=0;i<arguments.length;i++)e[this._len++]=arguments[i];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,r,n=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,h=this._xi,l=this._yi,u=t-h,c=e-l,f=Un(u*u+c*c),d=h,p=l,g=o.length;for(a<0&&(a=n+a),d-=(a%=n)*(u/=f),p-=a*(c/=f);0<u&&d<=t||u<0&&t<=d||0===u&&(0<c&&p<=e||c<0&&e<=p);)d+=u*(i=o[r=this._dashIdx]),p+=c*i,this._dashIdx=(r+1)%g,0<u&&d<h||u<0&&h<d||0<c&&p<l||c<0&&l<p||s[r%2?"moveTo":"lineTo"](0<=u?Xn(d,t):qn(d,t),0<=c?Xn(p,e):qn(p,e));u=d-t,c=p-e,this._dashOffset=-Un(u*u+c*c)},_dashedBezierTo:function(t,e,i,r,n,a){var o,s,h,l,u,c=this._dashSum,f=this._dashOffset,d=this._lineDash,p=this._ctx,g=this._xi,v=this._yi,_=_n,m=0,y=this._dashIdx,x=d.length,w=0;for(f<0&&(f=c+f),f%=c,o=0;o<1;o+=.1)s=_(g,t,i,n,o+.1)-_(g,t,i,n,o),h=_(v,e,r,a,o+.1)-_(v,e,r,a,o),m+=Un(s*s+h*h);for(;y<x&&!(f<(w+=d[y]));y++);for(o=(w-f)/m;o<=1;)l=_(g,t,i,n,o),u=_(v,e,r,a,o),y%2?p.moveTo(l,u):p.lineTo(l,u),o+=d[y]/m,y=(y+1)%x;y%2!=0&&p.lineTo(n,a),s=n-l,h=a-u,this._dashOffset=-Un(s*s+h*h)},_dashedQuadraticTo:function(t,e,i,r){var n=i,a=r;i=(i+2*t)/3,r=(r+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,r,n,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Zn&&(this.data=new Float32Array(t)))},getBoundingRect:function(){Hn[0]=Hn[1]=Wn[0]=Wn[1]=Number.MAX_VALUE,Nn[0]=Nn[1]=Vn[0]=Vn[1]=-Number.MAX_VALUE;for(var t,e,i,r,n,a,o,s,h,l,u,c,f,d,p=this.data,g=0,v=0,_=0,m=0,y=0;y<p.length;){var x=p[y++];switch(1===y&&(_=g=p[y],m=v=p[y+1]),x){case Fn.M:g=_=p[y++],v=m=p[y++],Wn[0]=_,Wn[1]=m,Vn[0]=_,Vn[1]=m;break;case Fn.L:Bn(g,v,p[y],p[y+1],Wn,Vn),g=p[y++],v=p[y++];break;case Fn.C:Rn(g,v,p[y++],p[y++],p[y++],p[y++],p[y],p[y+1],Wn,Vn),g=p[y++],v=p[y++];break;case Fn.Q:t=g,e=v,i=p[y++],r=p[y++],n=p[y],a=p[y+1],o=Wn,s=Vn,l=h=void 0,l=wn,u=Cn(Sn((h=kn)(t,i,n),1),0),c=Cn(Sn(h(e,r,a),1),0),f=l(t,i,n,u),d=l(e,r,a,c),o[0]=Sn(t,n,f),o[1]=Sn(e,a,d),s[0]=Cn(t,n,f),s[1]=Cn(e,a,d),g=p[y++],v=p[y++];break;case Fn.A:var w=p[y++],b=p[y++],k=p[y++],T=p[y++],S=p[y++],C=p[y++]+S;y+=1;var M=1-p[y++];1===y&&(_=jn(S)*k+w,m=Yn(S)*T+b),En(w,b,k,T,S,C,M,Wn,Vn),g=jn(C)*k+w,v=Yn(C)*T+b;break;case Fn.R:Bn(_=g=p[y++],m=v=p[y++],_+p[y++],m+p[y++],Wn,Vn);break;case Fn.Z:g=_,v=m}at(Hn,Hn,Wn),ot(Nn,Nn,Vn)}return 0===y&&(Hn[0]=Hn[1]=Nn[0]=Nn[1]=0),new ri(Hn[0],Hn[1],Nn[0]-Hn[0],Nn[1]-Hn[1])},rebuildPath:function(t){for(var e,i,r,n,a,o,s=this.data,h=this._ux,l=this._uy,u=this._len,c=0;c<u;){var f=s[c++];switch(1===c&&(e=r=s[c],i=n=s[c+1]),f){case Fn.M:e=r=s[c++],i=n=s[c++],t.moveTo(r,n);break;case Fn.L:a=s[c++],o=s[c++],(Gn(a-r)>h||Gn(o-n)>l||c===u-1)&&(t.lineTo(a,o),r=a,n=o);break;case Fn.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),r=s[c-2],n=s[c-1];break;case Fn.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),r=s[c-2],n=s[c-1];break;case Fn.A:var d=s[c++],p=s[c++],g=s[c++],v=s[c++],_=s[c++],m=s[c++],y=s[c++],x=s[c++],w=v<g?g:v,b=v<g?1:g/v,k=v<g?v/g:1,T=_+m;.001<Math.abs(g-v)?(t.translate(d,p),t.rotate(y),t.scale(b,k),t.arc(0,0,w,_,T,1-x),t.scale(1/b,1/k),t.rotate(-y),t.translate(-d,-p)):t.arc(d,p,w,_,T,1-x),1===c&&(e=jn(_)*g+d,i=Yn(_)*v+p),r=jn(T)*g+d,n=Yn(T)*v+p;break;case Fn.R:e=r=s[c],i=n=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case Fn.Z:t.closePath(),r=e,n=i}}}},Qn.CMD=Fn;var ta=2*Math.PI;function ea(t){return(t%=ta)<0&&(t+=ta),t}var ia=2*Math.PI;function ra(t,e,i,r,n,a,o,s,h){if(0===o)return!1;var l=o;s-=t,h-=e;var u=Math.sqrt(s*s+h*h);if(i<u-l||u+l<i)return!1;if(Math.abs(r-n)%ia<1e-4)return!0;if(a){var c=r;r=ea(n),n=ea(c)}else r=ea(r),n=ea(n);n<r&&(n+=ia);var f=Math.atan2(h,s);return f<0&&(f+=ia),r<=f&&f<=n||r<=f+ia&&f+ia<=n}function na(t,e,i,r,n,a){if(e<a&&r<a||a<e&&a<r)return 0;if(r===e)return 0;var o=r<e?1:-1,s=(a-e)/(r-e);1!=s&&0!=s||(o=r<e?.5:-.5);var h=s*(i-t)+t;return h===n?1/0:n<h?o:0}var aa=Qn.CMD,oa=2*Math.PI,sa=1e-4;var ha=[-1,-1,-1],la=[-1,-1];function ua(t,e,i,r,n,a,o,s,h,l){if(e<l&&r<l&&a<l&&s<l||l<e&&l<r&&l<a&&l<s)return 0;var u,c=function(t,e,i,r,n,a){var o=r+3*(e-i)-t,s=3*(i-2*e+t),h=3*(e-t),l=t-n,u=s*s-3*o*h,c=s*h-9*o*l,f=h*h-3*s*l,d=0;if(gn(u)&&gn(c)){if(gn(s))a[0]=0;else 0<=(T=-h/s)&&T<=1&&(a[d++]=T)}else{var p=c*c-4*u*f;if(gn(p)){var g=c/u,v=-g/2;0<=(T=-s/o+g)&&T<=1&&(a[d++]=T),0<=v&&v<=1&&(a[d++]=v)}else if(0<p){var _=sn(p),m=u*s+1.5*o*(-c+_),y=u*s+1.5*o*(-c-_);0<=(T=(-s-((m=m<0?-on(-m,cn):on(m,cn))+(y=y<0?-on(-y,cn):on(y,cn))))/(3*o))&&T<=1&&(a[d++]=T)}else{var x=(2*u*s-3*o*c)/(2*sn(u*u*u)),w=Math.acos(x)/3,b=sn(u),k=Math.cos(w),T=(-s-2*b*k)/(3*o),S=(v=(-s+b*(k+un*Math.sin(w)))/(3*o),(-s+b*(k-un*Math.sin(w)))/(3*o));0<=T&&T<=1&&(a[d++]=T),0<=v&&v<=1&&(a[d++]=v),0<=S&&S<=1&&(a[d++]=S)}}return d}(e,r,a,s,l,ha);if(0===c)return 0;for(var f,d,p=0,g=-1,v=0;v<c;v++){var _=ha[v],m=0===_||1===_?.5:1;_n(t,i,n,o,_)<h||(g<0&&(g=yn(e,r,a,s,la),la[1]<la[0]&&1<g&&(void 0,u=la[0],la[0]=la[1],la[1]=u),f=_n(e,r,a,s,la[0]),1<g&&(d=_n(e,r,a,s,la[1]))),2===g?_<la[0]?p+=f<e?m:-m:_<la[1]?p+=d<f?m:-m:p+=s<d?m:-m:_<la[0]?p+=f<e?m:-m:p+=s<f?m:-m)}return p}function ca(t,e,i,r,n,a,o,s){if(e<s&&r<s&&a<s||s<e&&s<r&&s<a)return 0;var h=function(t,e,i,r,n){var a=t-2*e+i,o=2*(e-t),s=t-r,h=0;if(gn(a)){if(vn(o))0<=(u=-s/o)&&u<=1&&(n[h++]=u)}else{var l=o*o-4*a*s;if(gn(l))0<=(u=-o/(2*a))&&u<=1&&(n[h++]=u);else if(0<l){var u,c=sn(l),f=(-o-c)/(2*a);0<=(u=(-o+c)/(2*a))&&u<=1&&(n[h++]=u),0<=f&&f<=1&&(n[h++]=f)}}return h}(e,r,a,s,ha);if(0===h)return 0;var l=kn(e,r,a);if(0<=l&&l<=1){for(var u=0,c=wn(e,r,a,l),f=0;f<h;f++){var d=0===ha[f]||1===ha[f]?.5:1;wn(t,i,n,ha[f])<o||(ha[f]<l?u+=c<e?d:-d:u+=a<c?d:-d)}return u}d=0===ha[0]||1===ha[0]?.5:1;return wn(t,i,n,ha[0])<o?0:a<e?d:-d}function fa(t,e,i,r,n,a,o,s){if(i<(s-=e)||s<-i)return 0;var h=Math.sqrt(i*i-s*s);ha[0]=-h,ha[1]=h;var l=Math.abs(r-n);if(l<1e-4)return 0;if(l%oa<1e-4){n=oa;var u=a?1:-1;return o>=ha[r=0]+t&&o<=ha[1]+t?u:0}if(a){h=r;r=ea(n),n=ea(h)}else r=ea(r),n=ea(n);n<r&&(n+=oa);for(var c=0,f=0;f<2;f++){var d=ha[f];if(o<d+t){var p=Math.atan2(s,d);u=a?1:-1;p<0&&(p=oa+p),(r<=p&&p<=n||r<=p+oa&&p+oa<=n)&&(p>Math.PI/2&&p<1.5*Math.PI&&(u=-u),c+=u)}}return c}function da(t,e,i,r,n){for(var a=0,o=0,s=0,h=0,l=0,u=0;u<t.length;){var c=t[u++];switch(c===aa.M&&1<u&&(i||(a+=na(o,s,h,l,r,n))),1===u&&(h=o=t[u],l=s=t[u+1]),c){case aa.M:o=h=t[u++],s=l=t[u++];break;case aa.L:if(i){if($n(o,s,t[u],t[u+1],e,r,n))return!0}else a+=na(o,s,t[u],t[u+1],r,n)||0;o=t[u++],s=t[u++];break;case aa.C:if(i){if(Kn(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],e,r,n))return!0}else a+=ua(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],r,n)||0;o=t[u++],s=t[u++];break;case aa.Q:if(i){if(Jn(o,s,t[u++],t[u++],t[u],t[u+1],e,r,n))return!0}else a+=ca(o,s,t[u++],t[u++],t[u],t[u+1],r,n)||0;o=t[u++],s=t[u++];break;case aa.A:var f=t[u++],d=t[u++],p=t[u++],g=t[u++],v=t[u++],_=t[u++];u+=1;var m=1-t[u++],y=Math.cos(v)*p+f,x=Math.sin(v)*g+d;1<u?a+=na(o,s,y,x,r,n):(h=y,l=x);var w=(r-f)*g/p+f;if(i){if(ra(f,d,g,v,v+_,m,e,w,n))return!0}else a+=fa(f,d,g,v,v+_,m,w,n);o=Math.cos(v+_)*p+f,s=Math.sin(v+_)*g+d;break;case aa.R:h=o=t[u++],l=s=t[u++];y=h+t[u++],x=l+t[u++];if(i){if($n(h,l,y,l,e,r,n)||$n(y,l,y,x,e,r,n)||$n(y,x,h,x,e,r,n)||$n(h,x,h,l,e,r,n))return!0}else a+=na(y,l,y,x,r,n),a+=na(h,x,h,l,r,n);break;case aa.Z:if(i){if($n(o,s,h,l,e,r,n))return!0}else a+=na(o,s,h,l,r,n);o=h,s=l}}return i||function(t,e){return Math.abs(t-e)<sa}(s,l)||(a+=na(o,s,h,l,r,n)||0),0!==a}var pa=Ci.prototype.getCanvasPattern,ga=Math.abs,va=new Qn(!0);function _a(t){Sr.call(this,t),this.path=null}_a.prototype={constructor:_a,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var i,r=this.style,n=this.path||va,a=r.hasStroke(),o=r.hasFill(),s=r.fill,h=r.stroke,l=o&&!!s.colorStops,u=a&&!!h.colorStops,c=o&&!!s.image,f=a&&!!h.image;r.bind(t,this,e),this.setTransform(t),this.__dirty&&(l&&(i=i||this.getBoundingRect(),this._fillGradient=r.getGradient(t,s,i)),u&&(i=i||this.getBoundingRect(),this._strokeGradient=r.getGradient(t,h,i)));l?t.fillStyle=this._fillGradient:c&&(t.fillStyle=pa.call(s,t)),u?t.strokeStyle=this._strokeGradient:f&&(t.strokeStyle=pa.call(h,t));var d=r.lineDash,p=r.lineDashOffset,g=!!t.setLineDash,v=this.getGlobalScale();if(n.setScale(v[0],v[1],this.segmentIgnoreThreshold),this.__dirtyPath||d&&!g&&a?(n.beginPath(t),d&&!g&&(n.setLineDash(d),n.setLineDashOffset(p)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o)if(null!=r.fillOpacity){var _=t.globalAlpha;t.globalAlpha=r.fillOpacity*r.opacity,n.fill(t),t.globalAlpha=_}else n.fill(t);if(d&&g&&(t.setLineDash(d),t.lineDashOffset=p),a)if(null!=r.strokeOpacity){_=t.globalAlpha;t.globalAlpha=r.strokeOpacity*r.opacity,n.stroke(t),t.globalAlpha=_}else n.stroke(t);d&&g&&t.setLineDash([]),null!=r.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,i){},createPathProxy:function(){this.path=new Qn},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var r=this.path;r=r||(this.path=new Qn),this.__dirtyPath&&(r.beginPath(),this.buildPath(r,this.shape,!1)),t=r.getBoundingRect()}if(this._rect=t,e.hasStroke()){var n=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){n.copy(t);var a=e.lineWidth,o=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),1e-10<o&&(n.width+=a/o,n.height+=a/o,n.x-=a/o/2,n.y-=a/o/2)}return n}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),r=this.getBoundingRect(),n=this.style;if(t=i[0],e=i[1],r.contain(t,e)){var a=this.path.data;if(n.hasStroke()){var o=n.lineWidth,s=n.strokeNoScale?this.getLineScale():1;if(1e-10<s&&(n.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),function(t,e,i,r){return da(t,e,!0,i,r)}(a,o/s,t,e)))return!0}if(n.hasFill())return function(t,e,i){return da(t,0,!1,e,i)}(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Sr.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(L(t))for(var r in t)t.hasOwnProperty(r)&&(i[r]=t[r]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&1e-10<ga(t[0]-1)&&1e-10<ga(t[3]-1)?Math.sqrt(ga(t[0]*t[3]-t[2]*t[1])):1}},_a.extend=function(n){function t(t){_a.call(this,t),n.style&&this.style.extendFrom(n.style,!1);var e=n.shape;if(e){this.shape=this.shape||{};var i=this.shape;for(var r in e)!i.hasOwnProperty(r)&&e.hasOwnProperty(r)&&(i[r]=e[r])}n.init&&n.init.call(this,t)}for(var e in b(t,_a),n)"style"!==e&&"shape"!==e&&(t.prototype[e]=n[e]);return t},b(_a,Sr);function ma(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}var ya=Qn.CMD,xa=[[],[],[]],wa=Math.sqrt,ba=Math.atan2,ka=function(t,e){var i,r,n,a,o,s=t.data,h=ya.M,l=ya.C,u=ya.L,c=ya.R,f=ya.A,d=ya.Q;for(a=n=0;n<s.length;){switch(i=s[n++],a=n,r=0,i){case h:case u:r=1;break;case l:r=3;break;case d:r=2;break;case f:var p=e[4],g=e[5],v=wa(e[0]*e[0]+e[1]*e[1]),_=wa(e[2]*e[2]+e[3]*e[3]),m=ba(-e[1]/_,e[0]/v);s[n]*=v,s[n++]+=p,s[n]*=_,s[n++]+=g,s[n++]*=v,s[n++]*=_,s[n++]+=m,s[n++]+=m,a=n+=2;break;case c:y[0]=s[n++],y[1]=s[n++],nt(y,y,e),s[a++]=y[0],s[a++]=y[1],y[0]+=s[n++],y[1]+=s[n++],nt(y,y,e),s[a++]=y[0],s[a++]=y[1]}for(o=0;o<r;o++){var y;(y=xa[o])[0]=s[n++],y[1]=s[n++],nt(y,y,e),s[a++]=y[0],s[a++]=y[1]}}},Ta=Math.sqrt,Sa=Math.sin,Ca=Math.cos,Ma=Math.PI,Aa=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(ma(t)*ma(e))},Pa=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Aa(t,e))};function La(t,e,i,r,n,a,o,s,h,l,u){var c=h*(Ma/180),f=Ca(c)*(t-i)/2+Sa(c)*(e-r)/2,d=-1*Sa(c)*(t-i)/2+Ca(c)*(e-r)/2,p=f*f/(o*o)+d*d/(s*s);1<p&&(o*=Ta(p),s*=Ta(p));var g=(n===a?-1:1)*Ta((o*o*(s*s)-o*o*(d*d)-s*s*(f*f))/(o*o*(d*d)+s*s*(f*f)))||0,v=g*o*d/s,_=g*-s*f/o,m=(t+i)/2+Ca(c)*v-Sa(c)*_,y=(e+r)/2+Sa(c)*v+Ca(c)*_,x=Pa([1,0],[(f-v)/o,(d-_)/s]),w=[(f-v)/o,(d-_)/s],b=[(-1*f-v)/o,(-1*d-_)/s],k=Pa(w,b);Aa(w,b)<=-1&&(k=Ma),1<=Aa(w,b)&&(k=0),0===a&&0<k&&(k-=2*Ma),1===a&&k<0&&(k+=2*Ma),u.addData(l,m,y,o,s,x,k,c,a)}var za=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Da=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Ba(t,e){var i=function(t){if(!t)return new Qn;for(var e,i=0,r=0,n=i,a=r,o=new Qn,s=Qn.CMD,h=t.match(za),l=0;l<h.length;l++){for(var u,c=h[l],f=c.charAt(0),d=c.match(Da)||[],p=d.length,g=0;g<p;g++)d[g]=parseFloat(d[g]);for(var v=0;v<p;){var _,m,y,x,w,b,k,T=i,S=r;switch(f){case"l":i+=d[v++],r+=d[v++],u=s.L,o.addData(u,i,r);break;case"L":i=d[v++],r=d[v++],u=s.L,o.addData(u,i,r);break;case"m":i+=d[v++],r+=d[v++],u=s.M,o.addData(u,i,r),n=i,a=r,f="l";break;case"M":i=d[v++],r=d[v++],u=s.M,o.addData(u,i,r),n=i,a=r,f="L";break;case"h":i+=d[v++],u=s.L,o.addData(u,i,r);break;case"H":i=d[v++],u=s.L,o.addData(u,i,r);break;case"v":r+=d[v++],u=s.L,o.addData(u,i,r);break;case"V":r=d[v++],u=s.L,o.addData(u,i,r);break;case"C":u=s.C,o.addData(u,d[v++],d[v++],d[v++],d[v++],d[v++],d[v++]),i=d[v-2],r=d[v-1];break;case"c":u=s.C,o.addData(u,d[v++]+i,d[v++]+r,d[v++]+i,d[v++]+r,d[v++]+i,d[v++]+r),i+=d[v-2],r+=d[v-1];break;case"S":_=i,m=r;var C=o.len(),M=o.data;e===s.C&&(_+=i-M[C-4],m+=r-M[C-3]),u=s.C,T=d[v++],S=d[v++],i=d[v++],r=d[v++],o.addData(u,_,m,T,S,i,r);break;case"s":_=i,m=r;C=o.len(),M=o.data;e===s.C&&(_+=i-M[C-4],m+=r-M[C-3]),u=s.C,T=i+d[v++],S=r+d[v++],i+=d[v++],r+=d[v++],o.addData(u,_,m,T,S,i,r);break;case"Q":T=d[v++],S=d[v++],i=d[v++],r=d[v++],u=s.Q,o.addData(u,T,S,i,r);break;case"q":T=d[v++]+i,S=d[v++]+r,i+=d[v++],r+=d[v++],u=s.Q,o.addData(u,T,S,i,r);break;case"T":_=i,m=r;C=o.len(),M=o.data;e===s.Q&&(_+=i-M[C-4],m+=r-M[C-3]),i=d[v++],r=d[v++],u=s.Q,o.addData(u,_,m,i,r);break;case"t":_=i,m=r;C=o.len(),M=o.data;e===s.Q&&(_+=i-M[C-4],m+=r-M[C-3]),i+=d[v++],r+=d[v++],u=s.Q,o.addData(u,_,m,i,r);break;case"A":y=d[v++],x=d[v++],w=d[v++],b=d[v++],k=d[v++],La(T=i,S=r,i=d[v++],r=d[v++],b,k,y,x,w,u=s.A,o);break;case"a":y=d[v++],x=d[v++],w=d[v++],b=d[v++],k=d[v++],La(T=i,S=r,i+=d[v++],r+=d[v++],b,k,y,x,w,u=s.A,o)}}"z"!==f&&"Z"!==f||(u=s.Z,o.addData(u),i=n,r=a),e=u}return o.toStatic(),o}(t);return(e=e||{}).buildPath=function(t){if(t.setData){t.setData(i.data),(e=t.getContext())&&t.rebuildPath(e)}else{var e=t;i.rebuildPath(e)}},e.applyTransform=function(t){ka(i,t),this.dirty(!0)},e}function Ia(t,e){return new _a(Ba(t,e))}var Oa=(Object.freeze||Object)({createFromString:Ia,extendFromString:function(t,e){return _a.extend(Ba(t,e))},mergePath:function(t,e){for(var i=[],r=t.length,n=0;n<r;n++){var a=t[n];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),i.push(a.path)}var o=new _a(e);return o.createPathProxy(),o.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},o}}),Ra=function(t){Sr.call(this,t)};Ra.prototype={constructor:Ra,type:"text",brush:function(t,e){var i=this.style;this.__dirty&&hr(i),i.fill=i.stroke=i.shadowBlur=i.shadowColor=i.shadowOffsetX=i.shadowOffsetY=null;var r=i.text;null!=r&&(r+=""),br(r,i)?(this.setTransform(t),ur(this,t,r,i,null,e),this.restoreTransform(t)):t.__attrCachedBy=_i.NONE},getBoundingRect:function(){var t=this.style;if(this.__dirty&&hr(t),!this._rect){var e=t.text;null!=e?e+="":e="";var i=Xi(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(i.x+=t.x||0,i.y+=t.y||0,mr(t.textStroke,t.textStrokeWidth)){var r=t.textStrokeWidth;i.x-=r/2,i.y-=r/2,i.width+=r,i.height+=r}this._rect=i}return this._rect}},b(Ra,Sr);var Ea=_a.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),Fa=Math.round;function Ha(t,e,i){if(!e)return t;var r=Fa(2*t);return(r+Fa(e))%2==0?r/2:(r+(i?1:-1))/2}var Na={},Wa=_a.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var i,r,n,a;this.subPixelOptimize?(function(t,e,i){if(e){var r=e.x,n=e.y,a=e.width,o=e.height;t.x=r,t.y=n,t.width=a,t.height=o;var s=i&&i.lineWidth;s&&(t.x=Ha(r,s,!0),t.y=Ha(n,s,!0),t.width=Math.max(Ha(r+a,s,!1)-t.x,0===a?0:1),t.height=Math.max(Ha(n+o,s,!1)-t.y,0===o?0:1))}}(Na,e,this.style),i=Na.x,r=Na.y,n=Na.width,a=Na.height,Na.r=e.r,e=Na):(i=e.x,r=e.y,n=e.width,a=e.height),e.r?er(t,e):t.rect(i,r,n,a),t.closePath()}}),Va=_a.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var i=e.cx,r=e.cy,n=e.rx,a=e.ry,o=.5522848*n,s=.5522848*a;t.moveTo(i-n,r),t.bezierCurveTo(i-n,r-s,i-o,r-a,i,r-a),t.bezierCurveTo(i+o,r-a,i+n,r-s,i+n,r),t.bezierCurveTo(i+n,r+s,i+o,r+a,i,r+a),t.bezierCurveTo(i-o,r+a,i-n,r+s,i-n,r),t.closePath()}}),Xa={},qa=_a.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,r,n,a;a=this.subPixelOptimize?(function(t,e,i){if(e){var r=e.x1,n=e.x2,a=e.y1,o=e.y2;t.x1=r,t.x2=n,t.y1=a,t.y2=o;var s=i&&i.lineWidth;s&&(Fa(2*r)===Fa(2*n)&&(t.x1=t.x2=Ha(r,s,!0)),Fa(2*a)===Fa(2*o)&&(t.y1=t.y2=Ha(a,s,!0)))}}(Xa,e,this.style),i=Xa.x1,r=Xa.y1,n=Xa.x2,Xa.y2):(i=e.x1,r=e.y1,n=e.x2,e.y2);var o=e.percent;0!==o&&(t.moveTo(i,r),o<1&&(n=i*(1-o)+n*o,a=r*(1-o)+a*o),t.lineTo(n,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}});function ja(t,e,i,r,n,a,o){var s=.5*(i-t),h=.5*(r-e);return(2*(e-i)+s+h)*o+(-3*(e-i)-2*s-h)*a+s*n+e}function Ya(t,e,i){var r=e.points,n=e.smooth;if(r&&2<=r.length){if(n&&"spline"!==n){var a=function(t,e,i,r){var n,a,o,s,h=[],l=[],u=[],c=[];if(r){o=[1/0,1/0],s=[-1/0,-1/0];for(var f=0,d=t.length;f<d;f++)at(o,o,t[f]),ot(s,s,t[f]);at(o,o,r[0]),ot(s,s,r[1])}for(f=0,d=t.length;f<d;f++){var p=t[f];if(i)n=t[f?f-1:d-1],a=t[(f+1)%d];else{if(0===f||f===d-1){h.push(j(t[f]));continue}n=t[f-1],a=t[f+1]}U(l,a,n),K(l,l,e);var g=tt(p,n),v=tt(p,a),_=g+v;0!==_&&(g/=_,v/=_),K(u,l,-g),K(c,l,v);var m=Y([],p,u),y=Y([],p,c);r&&(ot(m,m,o),at(m,m,s),ot(y,y,o),at(y,y,s)),h.push(m),h.push(y)}return i&&h.push(h.shift()),h}(r,n,i,e.smoothConstraint);t.moveTo(r[0][0],r[0][1]);for(var o=r.length,s=0;s<(i?o:o-1);s++){var h=a[2*s],l=a[2*s+1],u=r[(s+1)%o];t.bezierCurveTo(h[0],h[1],l[0],l[1],u[0],u[1])}}else{"spline"===n&&(r=function(t,e){for(var i=t.length,r=[],n=0,a=1;a<i;a++)n+=tt(t[a-1],t[a]);var o=n/2;o=o<i?i:o;for(a=0;a<o;a++){var s,h,l,u=a/(o-1)*(e?i:i-1),c=Math.floor(u),f=u-c,d=t[c%i];l=e?(s=t[(c-1+i)%i],h=t[(c+1)%i],t[(c+2)%i]):(s=t[0===c?c:c-1],h=t[i-2<c?i-1:c+1],t[i-3<c?i-1:c+2]);var p=f*f,g=f*p;r.push([ja(s[0],d[0],h[0],l[0],f,p,g),ja(s[1],d[1],h[1],l[1],f,p,g)])}return r}(r,i)),t.moveTo(r[0][0],r[0][1]);s=1;for(var c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}i&&t.closePath()}}function Ua(t){this.colorStops=t||[]}var Ga=_a.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){Ya(t,e,!0)}}),Za=_a.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){Ya(t,e,!1)}});Ua.prototype={constructor:Ua,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};function Qa(t,e,i,r,n,a){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==i?1:i,this.y2=null==r?0:r,this.type="linear",this.global=a||!1,Ua.call(this,n)}Qa.prototype={constructor:Qa},b(Qa,Ua);var $a=/[\s,]+/;function Ka(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}Ka.prototype.parse=function(t,e){e=e||{};var i=function(t){for(P(t)&&(t=(new DOMParser).parseFromString(t,"text/xml")),9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}(t);if(!i)throw new Error("Illegal svg");var r=new ni;this._root=r;var n=i.getAttribute("viewBox")||"",a=parseFloat(i.getAttribute("width")||e.width),o=parseFloat(i.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(o)&&(o=null),no(i,r,null,!0);for(var s,h,l=i.firstChild;l;)this._parseNode(l,r),l=l.nextSibling;if(n){var u=E(n).split($a);4<=u.length&&(s={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])})}if(s&&null!=a&&null!=o&&(h=function(t,e,i){var r=e/t.width,n=i/t.height,a=Math.min(r,n),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+i/2];return{scale:o,position:s}}(s,a,o),!e.ignoreViewBox)){var c=r;(r=new ni).add(c),c.scale=h.scale.slice(),c.position=h.position.slice()}return e.ignoreRootClip||null==a||null==o||r.setClipPath(new Wa({shape:{x:0,y:0,width:a,height:o}})),{root:r,width:a,height:o,viewBoxRect:s,viewBoxTransform:h}},Ka.prototype._parseNode=function(t,e){var i,r,n=t.nodeName.toLowerCase();if("defs"===n?this._isDefine=!0:"text"===n&&(this._isText=!0),this._isDefine){if(r=to[n]){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else(r=Ja[n])&&(i=r.call(this,t,e),e.add(i));for(var s=t.firstChild;s;)1===s.nodeType&&this._parseNode(s,i),3===s.nodeType&&this._isText&&this._parseText(s,i),s=s.nextSibling;"defs"===n?this._isDefine=!1:"text"===n&&(this._isText=!1)},Ka.prototype._parseText=function(t,e){if(1===t.nodeType){var i=t.getAttribute("dx")||0,r=t.getAttribute("dy")||0;this._textX+=parseFloat(i),this._textY+=parseFloat(r)}var n=new Ra({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});eo(e,n),no(t,n,this._defs);var a=n.style.fontSize;a&&a<9&&(n.style.fontSize=9,n.scale=n.scale||[1,1],n.scale[0]*=a/9,n.scale[1]*=a/9);var o=n.getBoundingRect();return this._textX+=o.width,e.add(n),n};var Ja={g:function(t,e){var i=new ni;return eo(e,i),no(t,i,this._defs),i},rect:function(t,e){var i=new Wa;return eo(e,i),no(t,i,this._defs),i.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),i},circle:function(t,e){var i=new Ea;return eo(e,i),no(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),i},line:function(t,e){var i=new qa;return eo(e,i),no(t,i,this._defs),i.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),i},ellipse:function(t,e){var i=new Va;return eo(e,i),no(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),i},polygon:function(t,e){var i=t.getAttribute("points");i=i&&io(i);var r=new Ga({shape:{points:i||[]}});return eo(e,r),no(t,r,this._defs),r},polyline:function(t,e){var i=new _a;eo(e,i),no(t,i,this._defs);var r=t.getAttribute("points");return r=r&&io(r),new Za({shape:{points:r||[]}})},image:function(t,e){var i=new Cr;return eo(e,i),no(t,i,this._defs),i.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),i},text:function(t,e){var i=t.getAttribute("x")||0,r=t.getAttribute("y")||0,n=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0;this._textX=parseFloat(i)+parseFloat(n),this._textY=parseFloat(r)+parseFloat(a);var o=new ni;return eo(e,o),no(t,o,this._defs),o},tspan:function(t,e){var i=t.getAttribute("x"),r=t.getAttribute("y");null!=i&&(this._textX=parseFloat(i)),null!=r&&(this._textY=parseFloat(r));var n=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0,o=new ni;return eo(e,o),no(t,o,this._defs),this._textX+=n,this._textY+=a,o},path:function(t,e){var i=Ia(t.getAttribute("d")||"");return eo(e,i),no(t,i,this._defs),i}},to={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),i=parseInt(t.getAttribute("y1")||0,10),r=parseInt(t.getAttribute("x2")||10,10),n=parseInt(t.getAttribute("y2")||0,10),a=new Qa(e,i,r,n);return function(t,e){var i=t.firstChild;for(;i;){if(1===i.nodeType){var r=i.getAttribute("offset");r=0<r.indexOf("%")?parseInt(r,10)/100:r?parseFloat(r):0;var n=i.getAttribute("stop-color")||"#000000";e.addColorStop(r,n)}i=i.nextSibling}}(t,a),a},radialgradient:function(t){}};function eo(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),v(e.__inheritedStyle,t.__inheritedStyle))}function io(t){for(var e=E(t).split($a),i=[],r=0;r<e.length;r+=2){var n=parseFloat(e[r]),a=parseFloat(e[r+1]);i.push([n,a])}return i}var ro={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"};function no(t,e,i,r){var n=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(function(t,e){var i=t.getAttribute("transform");if(i){i=i.replace(/,/g," ");var r=null,n=[];i.replace(so,function(t,e,i){n.push(e,i)});for(var a=n.length-1;0<a;a-=2){var o=n[a],s=n[a-1];switch(r=r||Ft(),s){case"translate":o=E(o).split($a),Vt(r,r,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=E(o).split($a),qt(r,r,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=E(o).split($a),Xt(r,r,parseFloat(o[0]));break;case"skew":o=E(o).split($a),console.warn("Skew transform is not supported yet");break;case"matrix":o=E(o).split($a);r[0]=parseFloat(o[0]),r[1]=parseFloat(o[1]),r[2]=parseFloat(o[2]),r[3]=parseFloat(o[3]),r[4]=parseFloat(o[4]),r[5]=parseFloat(o[5])}}e.setLocalTransform(r)}}(t,e),g(n,function(t){var e=t.getAttribute("style"),i={};if(!e)return i;var r,n={};ho.lastIndex=0;for(;null!=(r=ho.exec(e));)n[r[1]]=r[2];for(var a in ro)ro.hasOwnProperty(a)&&null!=n[a]&&(i[ro[a]]=n[a]);return i}(t)),!r))for(var o in ro)if(ro.hasOwnProperty(o)){var s=t.getAttribute(o);null!=s&&(n[ro[o]]=s)}var h=a?"textFill":"fill",l=a?"textStroke":"stroke";e.style=e.style||new xi;var u=e.style;null!=n.fill&&u.set(h,oo(n.fill,i)),null!=n.stroke&&u.set(l,oo(n.stroke,i)),T(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=n[t]&&u.set(e,parseFloat(n[t]))}),n.textBaseline&&"auto"!==n.textBaseline||(n.textBaseline="alphabetic"),"alphabetic"===n.textBaseline&&(n.textBaseline="bottom"),"start"===n.textAlign&&(n.textAlign="left"),"end"===n.textAlign&&(n.textAlign="right"),T(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=n[t]&&u.set(t,n[t])}),n.lineDash&&(e.style.lineDash=E(n.lineDash).split($a)),u[l]&&"none"!==u[l]&&(e[l]=!0),e.__inheritedStyle=n}var ao=/url\(\s*#(.*?)\)/;function oo(t,e){var i=e&&t&&t.match(ao);return i?e[E(i[1])]:t}var so=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g;var ho=/([^\s:;]+)\s*:\s*([^:;]+)/g;var lo=_a.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e.length;i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t.length;i++)t[i].path||t[i].createPathProxy(),t[i].path.setScale(e[0],e[1],t[i].segmentIgnoreThreshold)},buildPath:function(t,e){for(var i=e.paths||[],r=0;r<i.length;r++)i[r].buildPath(t,i[r].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),_a.prototype.getBoundingRect.call(this)}});function uo(t){Sr.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}uo.prototype.incremental=!0,uo.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},uo.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},uo.prototype.addDisplayables=function(t,e){e=e||!1;for(var i=0;i<t.length;i++)this.addDisplayable(t[i],e)},uo.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},uo.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},uo.prototype.brush=function(t,e){for(var i=this._cursor;i<this._displayables.length;i++){(r=this._displayables[i]).beforeBrush&&r.beforeBrush(t),r.brush(t,i===this._cursor?null:this._displayables[i-1]),r.afterBrush&&r.afterBrush(t)}this._cursor=i;for(i=0;i<this._temporaryDisplayables.length;i++){var r;(r=this._temporaryDisplayables[i]).beforeBrush&&r.beforeBrush(t),r.brush(t,0===i?null:this._temporaryDisplayables[i-1]),r.afterBrush&&r.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var co=[];uo.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new ri(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var i=this._displayables[e],r=i.getBoundingRect().clone();i.needLocalTransform()&&r.applyTransform(i.getLocalTransform(co)),t.union(r)}this._rect=t}return this._rect},uo.prototype.contain=function(t,e){var i=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(i[0],i[1]))for(var r=0;r<this._displayables.length;r++){if(this._displayables[r].contain(t,e))return!0}return!1},b(uo,Sr);var fo=_a.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,r=e.cy,n=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,h=Math.cos(a),l=Math.sin(a);t.moveTo(h*n+i,l*n+r),t.arc(i,r,n,a,o,!s)}}),po=[];function go(t,e,i){var r=t.cpx2,n=t.cpy2;return null===r||null===n?[(i?mn:_n)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?mn:_n)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?bn:wn)(t.x1,t.cpx1,t.x2,e),(i?bn:wn)(t.y1,t.cpy1,t.y2,e)]}function vo(t,e,i,r,n){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==i?.5:i,this.type="radial",this.global=n||!1,Ua.call(this,r)}var _o,mo=_a.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,r=e.y1,n=e.x2,a=e.y2,o=e.cpx1,s=e.cpy1,h=e.cpx2,l=e.cpy2,u=e.percent;0!==u&&(t.moveTo(i,r),null==h||null==l?(u<1&&(Tn(i,o,n,u,po),o=po[1],n=po[2],Tn(r,s,a,u,po),s=po[1],a=po[2]),t.quadraticCurveTo(o,s,n,a)):(u<1&&(xn(i,o,h,n,u,po),o=po[1],h=po[2],n=po[3],xn(r,s,l,a,u,po),s=po[1],l=po[2],a=po[3]),t.bezierCurveTo(o,s,h,l,n,a)))},pointAt:function(t){return go(this.shape,t,!1)},tangentAt:function(t){var e=go(this.shape,t,!0);return J(e,e)}}),yo=_a.extend({type:"droplet",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,r=e.cy,n=e.width,a=e.height;t.moveTo(i,r+n),t.bezierCurveTo(i+n,r+n,i+3*n/2,r-n/3,i,r-a),t.bezierCurveTo(i-3*n/2,r-n/3,i-n,r+n,i,r+n),t.closePath()}}),xo=_a.extend({type:"heart",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,r=e.cy,n=e.width,a=e.height;t.moveTo(i,r),t.bezierCurveTo(i+n/2,r-2*a/3,i+2*n,r+a/3,i,r+a),t.bezierCurveTo(i-2*n,r+a/3,i-n/2,r-2*a/3,i,r)}}),wo=Math.PI,bo=Math.sin,ko=Math.cos,To=_a.extend({type:"isogon",shape:{x:0,y:0,r:0,n:0},buildPath:function(t,e){var i=e.n;if(i&&!(i<2)){var r=e.x,n=e.y,a=e.r,o=2*wo/i,s=-wo/2;t.moveTo(r+a*ko(s),n+a*bo(s));for(var h=0,l=i-1;h<l;h++)s+=o,t.lineTo(r+a*ko(s),n+a*bo(s));t.closePath()}}}),So=_a.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,r=e.cy,n=2*Math.PI;t.moveTo(i+e.r,r),t.arc(i,r,e.r,0,n,!1),t.moveTo(i+e.r0,r),t.arc(i,r,e.r0,0,n,!0)}}),Co=Math.sin,Mo=Math.cos,Ao=Math.PI/180,Po=_a.extend({type:"rose",shape:{cx:0,cy:0,r:[],k:0,n:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,r,n,a=e.r,o=e.k,s=e.n,h=e.cx,l=e.cy;t.moveTo(h,l);for(var u=0,c=a.length;u<c;u++){n=a[u];for(var f=0;f<=360*s;f++)i=n*Co(o/s*f%360*Ao)*Mo(f*Ao)+h,r=n*Co(o/s*f%360*Ao)*Co(f*Ao)+l,t.lineTo(i,r)}}}),Lo=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],zo=_a.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:(_o=_a.prototype.brush,_.browser.ie&&11<=_.browser.version?function(){var t,e=this.__clipPaths,i=this.style;if(e)for(var r=0;r<e.length;r++){var n=e[r],a=n&&n.shape,o=n&&n.type;if(a&&("sector"===o&&a.startAngle===a.endAngle||"rect"===o&&(!a.width||!a.height))){for(var s=0;s<Lo.length;s++)Lo[s][2]=i[Lo[s][0]],i[Lo[s][0]]=Lo[s][1];t=!0;break}}if(_o.apply(this,arguments),t)for(s=0;s<Lo.length;s++)i[Lo[s][0]]=Lo[s][2]}:_o),buildPath:function(t,e){var i=e.cx,r=e.cy,n=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,h=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*n+i,u*n+r),t.lineTo(l*a+i,u*a+r),t.arc(i,r,a,o,s,!h),t.lineTo(Math.cos(s)*n+i,Math.sin(s)*n+r),0!==n&&t.arc(i,r,n,s,o,h),t.closePath()}}),Do=Math.PI,Bo=Math.cos,Io=Math.sin,Oo=_a.extend({type:"star",shape:{cx:0,cy:0,n:3,r0:null,r:0},buildPath:function(t,e){var i=e.n;if(i&&!(i<2)){var r=e.cx,n=e.cy,a=e.r,o=e.r0;null==o&&(o=4<i?a*Bo(2*Do/i)/Bo(Do/i):a/3);var s=Do/i,h=-Do/2,l=r+a*Bo(h),u=n+a*Io(h);h+=s,t.moveTo(l,u);for(var c,f=0,d=2*i-1;f<d;f++)c=f%2==0?o:a,t.lineTo(r+c*Bo(h),n+c*Io(h)),h+=s;t.closePath()}}}),Ro=Math.cos,Eo=Math.sin,Fo=_a.extend({type:"trochoid",shape:{cx:0,cy:0,r:0,r0:0,d:0,location:"out"},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,r,n,a,o=e.r,s=e.r0,h=e.d,l=e.cx,u=e.cy,c="out"===e.location?1:-1;if(!(e.location&&o<=s)){var f,d=0,p=1;for(i=(o+c*s)*Ro(0)-c*h*Ro(0)+l,r=(o+c*s)*Eo(0)-h*Eo(0)+u,t.moveTo(i,r);s*++d%(o+c*s)!=0;);for(;f=Math.PI/180*p,n=(o+c*s)*Ro(f)-c*h*Ro((o/s+c)*f)+l,a=(o+c*s)*Eo(f)-h*Eo((o/s+c)*f)+u,t.lineTo(n,a),++p<=s*d/(o+c*s)*360;);}}});vo.prototype={constructor:vo},b(vo,Ua);function Ho(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}var No=Qn.CMD,Wo=Array.prototype.join,Vo="none",Xo=Math.round,qo=Math.sin,jo=Math.cos,Yo=Math.PI,Uo=2*Math.PI,Go=180/Yo,Zo=1e-4;function Qo(t){return Xo(1e4*t)/1e4}function $o(t){return t<Zo&&-Zo<t}function Ko(t,e){e&&Jo(t,"transform","matrix("+Wo.call(e,",")+")")}function Jo(t,e,i){i&&("linear"===i.type||"radial"===i.type)||t.setAttribute(e,i)}function ts(t,e,i,r){if(function(t,e){var i=e?t.textFill:t.fill;return null!=i&&i!==Vo}(e,i)){var n=i?e.textFill:e.fill;Jo(t,"fill",n="transparent"===n?Vo:n),Jo(t,"fill-opacity",null!=e.fillOpacity?e.fillOpacity*e.opacity:e.opacity)}else Jo(t,"fill",Vo);if(function(t,e){var i=e?t.textStroke:t.stroke;return null!=i&&i!==Vo}(e,i)){var a=i?e.textStroke:e.stroke;Jo(t,"stroke",a="transparent"===a?Vo:a),Jo(t,"stroke-width",(i?e.textStrokeWidth:e.lineWidth)/(!i&&e.strokeNoScale?r.getLineScale():1)),Jo(t,"paint-order",i?"stroke":"fill"),Jo(t,"stroke-opacity",null!=e.strokeOpacity?e.strokeOpacity:e.opacity),e.lineDash?(Jo(t,"stroke-dasharray",e.lineDash.join(",")),Jo(t,"stroke-dashoffset",Xo(e.lineDashOffset||0))):Jo(t,"stroke-dasharray",""),e.lineCap&&Jo(t,"stroke-linecap",e.lineCap),e.lineJoin&&Jo(t,"stroke-linejoin",e.lineJoin),e.miterLimit&&Jo(t,"stroke-miterlimit",e.miterLimit)}else Jo(t,"stroke",Vo)}var es={};es.brush=function(t){var e=t.style,i=t.__svgEl;i||(i=Ho("path"),t.__svgEl=i),t.path||t.createPathProxy();var r=t.path;if(t.__dirtyPath){r.beginPath(),r.subPixelOptimize=!1,t.buildPath(r,t.shape),t.__dirtyPath=!1;var n=function(t){for(var e=[],i=t.data,r=t.len(),n=0;n<r;){var a="",o=0;switch(i[n++]){case No.M:a="M",o=2;break;case No.L:a="L",o=2;break;case No.Q:a="Q",o=4;break;case No.C:a="C",o=6;break;case No.A:var s=i[n++],h=i[n++],l=i[n++],u=i[n++],c=i[n++],f=i[n++],d=i[n++],p=i[n++],g=Math.abs(f),v=$o(g-Uo)||(p?Uo<=f:Uo<=-f),_=0<f?f%Uo:f%Uo+Uo,m=!1;m=!!v||!$o(g)&&Yo<=_==!!p;var y=Qo(s+l*jo(c)),x=Qo(h+u*qo(c));v&&(f=p?Uo-1e-4:1e-4-Uo,m=!0,9===n&&e.push("M",y,x));var w=Qo(s+l*jo(c+f)),b=Qo(h+u*qo(c+f));e.push("A",Qo(l),Qo(u),Xo(d*Go),+m,+p,w,b);break;case No.Z:a="Z";break;case No.R:w=Qo(i[n++]),b=Qo(i[n++]);var k=Qo(i[n++]),T=Qo(i[n++]);e.push("M",w,b,"L",w+k,b,"L",w+k,b+T,"L",w,b+T,"L",w,b)}a&&e.push(a);for(var S=0;S<o;S++)e.push(Qo(i[n++]))}return e.join(" ")}(r);n.indexOf("NaN")<0&&Jo(i,"d",n)}ts(i,e,!1,t),Ko(i,t.transform),null!=e.text?hs(t,t.getBoundingRect()):us(t)};var is={brush:function(t){var e=t.style,i=e.image;i instanceof HTMLImageElement&&(i=i.src);if(i){var r=e.x||0,n=e.y||0,a=e.width,o=e.height,s=t.__svgEl;s||(s=Ho("image"),t.__svgEl=s),i!==t.__imageSrc&&(function(t,e,i){t.setAttributeNS("http://www.w3.org/1999/xlink",e,i)}(s,"href",i),t.__imageSrc=i),Jo(s,"width",a),Jo(s,"height",o),Jo(s,"x",r),Jo(s,"y",n),Ko(s,t.transform),null!=e.text?hs(t,t.getBoundingRect()):us(t)}}},rs={},ns=new ri,as={},os=[],ss={left:"start",right:"end",center:"middle",middle:"middle"},hs=function(t,e){var i=t.style,r=t.transform,n=t instanceof Ra||i.transformText;t.__dirty&&hr(i);var a=i.text;if(null!=a&&(a+=""),br(a,i)){null==a&&(a=""),!n&&r&&(ns.copy(e),ns.applyTransform(r),e=ns);var o=t.__textSvgEl;o||(o=Ho("text"),t.__textSvgEl=o);var s=o.style,h=i.font||Ni,l=o.__computedFont;h!==o.__styleFont&&(s.font=o.__styleFont=h,l=o.__computedFont=s.font);var u=i.textPadding,c=i.textLineHeight,f=t.__textCotentBlock;f&&!t.__dirtyText||(f=t.__textCotentBlock=Ki(a,l,u,c,i.truncate));var d=f.outerHeight,p=f.lineHeight;vr(as,t,i,e);var g=as.baseX,v=as.baseY,_=as.textAlign||"left",m=as.textVerticalAlign;!function(t,e,i,r,n,a,o){Ht(os),e&&i&&Nt(os,i);var s=r.textRotation;if(n&&s){var h=r.textOrigin;"center"===h?(a=n.width/2+n.x,o=n.height/2+n.y):h&&(a=h[0]+n.x,o=h[1]+n.y),os[4]-=a,os[5]-=o,Xt(os,os,s),os[4]+=a,os[5]+=o}Ko(t,os)}(o,n,r,i,e,g,v);var y=g,x=ji(v,d,m);u&&(y=function(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}(g,_,u),x+=u[0]),x+=p/2,ts(o,i,!0,t);var w=f.canCacheByTextString,b=t.__tspanList||(t.__tspanList=[]),k=b.length;if(w&&t.__canCacheByTextString&&t.__text===a){if(t.__dirtyText&&k)for(var T=0;T<k;++T)ls(b[T],_,y,x+T*p)}else{t.__text=a,t.__canCacheByTextString=w;var S=f.lines,C=S.length;for(T=0;T<C;T++){var M=b[T],A=S[T];M?M.__zrText!==A&&(M.innerHTML="",M.appendChild(document.createTextNode(A))):(M=b[T]=Ho("tspan"),o.appendChild(M),M.appendChild(document.createTextNode(A))),ls(M,_,y,x+T*p)}if(C<k){for(;T<k;T++)o.removeChild(b[T]);b.length=C}}}};function ls(t,e,i,r){Jo(t,"dominant-baseline","middle"),Jo(t,"text-anchor",ss[e]),Jo(t,"x",i),Jo(t,"y",r)}function us(t){t&&t.__textSvgEl&&(t.__textSvgEl.parentNode&&t.__textSvgEl.parentNode.removeChild(t.__textSvgEl),t.__textSvgEl=null,t.__tspanList=[],t.__text=null)}function cs(){}function fs(t,e){for(var i=0,r=e.length,n=0,a=0;i<r;i++){var o=e[i];if(o.removed){for(s=[],h=a;h<a+o.count;h++)s.push(h);o.indices=s,a+=o.count}else{for(var s=[],h=n;h<n+o.count;h++)s.push(h);o.indices=s,n+=o.count,o.added||(a+=o.count)}}return e}rs.drawRectText=hs,rs.brush=function(t){null!=t.style.text?hs(t,!1):us(t)},cs.prototype={diff:function(h,l,t){t=t||function(t,e){return t===e},this.equals=t;var u=this;h=h.slice();var c=(l=l.slice()).length,f=h.length,d=1,e=c+f,p=[{newPos:-1,components:[]}],i=this.extractCommon(p[0],l,h,0);if(p[0].newPos+1>=c&&f<=i+1){for(var r=[],n=0;n<l.length;n++)r.push(n);return[{indices:r,count:l.length}]}function a(){for(var t=-1*d;t<=d;t+=2){var e,i=p[t-1],r=p[t+1],n=(r?r.newPos:0)-t;i&&(p[t-1]=void 0);var a=i&&i.newPos+1<c,o=r&&0<=n&&n<f;if(a||o){if(!a||o&&i.newPos<r.newPos?(e={newPos:(s=r).newPos,components:s.components.slice(0)},u.pushComponent(e.components,void 0,!0)):((e=i).newPos++,u.pushComponent(e.components,!0,void 0)),n=u.extractCommon(e,l,h,t),e.newPos+1>=c&&f<=n+1)return fs(u,e.components,l,h);p[t]=e}else p[t]=void 0}var s;d++}for(;d<=e;){var o=a();if(o)return o}},pushComponent:function(t,e,i){var r=t[t.length-1];r&&r.added===e&&r.removed===i?t[t.length-1]={count:r.count+1,added:e,removed:i}:t.push({count:1,added:e,removed:i})},extractCommon:function(t,e,i,r){for(var n=e.length,a=i.length,o=t.newPos,s=o-r,h=0;o+1<n&&s+1<a&&this.equals(e[o+1],i[s+1]);)o++,s++,h++;return h&&t.components.push({count:h}),t.newPos=o,s},tokenize:function(t){return t.slice()},join:function(t){return t.slice()}};var ds=new cs;function ps(t,e,i,r,n){this._zrId=t,this._svgRoot=e,this._tagNames="string"==typeof i?[i]:i,this._markLabel=r,this._domName=n||"_dom",this.nextId=0}function gs(t,e){ps.call(this,t,e,["linearGradient","radialGradient"],"__gradient_in_use__")}function vs(t,e){ps.call(this,t,e,"clipPath","__clippath_in_use__")}function _s(t,e){ps.call(this,t,e,["filter"],"__filter_in_use__","_shadowDom")}function ms(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY||t.textShadowBlur||t.textShadowOffsetX||t.textShadowOffsetY)}function ys(t){return parseInt(t,10)}function xs(t,e){return e&&t&&e.parentNode!==t}function ws(t,e,i){if(xs(t,e)&&i){var r=i.nextSibling;r?t.insertBefore(e,r):t.appendChild(e)}}function bs(t,e){if(xs(t,e)){var i=t.firstChild;i?t.insertBefore(e,i):t.appendChild(e)}}function ks(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)}function Ts(t){return t.__textSvgEl}function Ss(t){return t.__svgEl}ps.prototype.createElement=Ho,ps.prototype.getDefs=function(t){var e=this._svgRoot,r=this._svgRoot.getElementsByTagName("defs");return 0===r.length?t?((r=e.insertBefore(this.createElement("defs"),e.firstChild)).contains||(r.contains=function(t){var e=r.children;if(!e)return!1;for(var i=e.length-1;0<=i;--i)if(e[i]===t)return!0;return!1}),r):null:r[0]},ps.prototype.update=function(t,e){if(t){var i=this.getDefs(!1);if(t[this._domName]&&i.contains(t[this._domName]))"function"==typeof e&&e(t);else{var r=this.add(t);r&&(t[this._domName]=r)}}},ps.prototype.addDom=function(t){this.getDefs(!0).appendChild(t)},ps.prototype.removeDom=function(t){var e=this.getDefs(!1);e&&t[this._domName]&&(e.removeChild(t[this._domName]),t[this._domName]=null)},ps.prototype.getDoms=function(){var i=this.getDefs(!1);if(!i)return[];var r=[];return T(this._tagNames,function(t){var e=i.getElementsByTagName(t);r=r.concat([].slice.call(e))}),r},ps.prototype.markAllUnused=function(){var t=this.getDoms(),e=this;T(t,function(t){t[e._markLabel]="0"})},ps.prototype.markUsed=function(t){t&&(t[this._markLabel]="1")},ps.prototype.removeUnused=function(){var e=this.getDefs(!1);if(e){var t=this.getDoms(),i=this;T(t,function(t){"1"!==t[i._markLabel]&&e.removeChild(t)})}},ps.prototype.getSvgProxy=function(t){return t instanceof _a?es:t instanceof Cr?is:t instanceof Ra?rs:es},ps.prototype.getTextSvgElement=function(t){return t.__textSvgEl},ps.prototype.getSvgElement=function(t){return t.__svgEl},b(gs,ps),gs.prototype.addWithoutUpdate=function(a,o){if(o&&o.style){var s=this;T(["fill","stroke"],function(t){if(o.style[t]&&("linear"===o.style[t].type||"radial"===o.style[t].type)){var e,i=o.style[t],r=s.getDefs(!0);i._dom?(e=i._dom,r.contains(i._dom)||s.addDom(e)):e=s.add(i),s.markUsed(o);var n=e.getAttribute("id");a.setAttribute(t,"url(#"+n+")")}})}},gs.prototype.add=function(t){var e;if("linear"===t.type)e=this.createElement("linearGradient");else{if("radial"!==t.type)return Ye("Illegal gradient type."),null;e=this.createElement("radialGradient")}return t.id=t.id||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-gradient-"+t.id),this.updateDom(t,e),this.addDom(e),e},gs.prototype.update=function(i){var r=this;ps.prototype.update.call(this,i,function(){var t=i.type,e=i._dom.tagName;"linear"===t&&"linearGradient"===e||"radial"===t&&"radialGradient"===e?r.updateDom(i,i._dom):(r.removeDom(i),r.add(i))})},gs.prototype.updateDom=function(t,e){if("linear"===t.type)e.setAttribute("x1",t.x),e.setAttribute("y1",t.y),e.setAttribute("x2",t.x2),e.setAttribute("y2",t.y2);else{if("radial"!==t.type)return void Ye("Illegal gradient type.");e.setAttribute("cx",t.x),e.setAttribute("cy",t.y),e.setAttribute("r",t.r)}t.global?e.setAttribute("gradientUnits","userSpaceOnUse"):e.setAttribute("gradientUnits","objectBoundingBox"),e.innerHTML="";for(var i=t.colorStops,r=0,n=i.length;r<n;++r){var a=this.createElement("stop");a.setAttribute("offset",100*i[r].offset+"%");var o=i[r].color;if(-1<o.indexOf("rgba")){var s=ye(o)[3],h=we(o);a.setAttribute("stop-color","#"+h),a.setAttribute("stop-opacity",s)}else a.setAttribute("stop-color",i[r].color);e.appendChild(a)}t._dom=e},gs.prototype.markUsed=function(t){if(t.style){var e=t.style.fill;e&&e._dom&&ps.prototype.markUsed.call(this,e._dom),(e=t.style.stroke)&&e._dom&&ps.prototype.markUsed.call(this,e._dom)}},b(vs,ps),vs.prototype.update=function(t){var e=this.getSvgElement(t);e&&this.updateDom(e,t.__clipPaths,!1);var i=this.getTextSvgElement(t);i&&this.updateDom(i,t.__clipPaths,!0),this.markUsed(t)},vs.prototype.updateDom=function(t,e,i){if(e&&0<e.length){var r,n,a=this.getDefs(!0),o=e[0],s=i?"_textDom":"_dom";o[s]?(n=o[s].getAttribute("id"),r=o[s],a.contains(r)||a.appendChild(r)):(n="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,(r=this.createElement("clipPath")).setAttribute("id",n),a.appendChild(r),o[s]=r);var h=this.getSvgProxy(o);if(o.transform&&o.parent.invTransform&&!i){var l=Array.prototype.slice.call(o.transform);Wt(o.transform,o.parent.invTransform,o.transform),h.brush(o),o.transform=l}else h.brush(o);var u=this.getSvgElement(o);r.innerHTML="",r.appendChild(u.cloneNode()),t.setAttribute("clip-path","url(#"+n+")"),1<e.length&&this.updateDom(r,e.slice(1),i)}else t&&t.setAttribute("clip-path","none")},vs.prototype.markUsed=function(t){var e=this;t.__clipPaths&&T(t.__clipPaths,function(t){t._dom&&ps.prototype.markUsed.call(e,t._dom),t._textDom&&ps.prototype.markUsed.call(e,t._textDom)})},b(_s,ps),_s.prototype.addWithoutUpdate=function(t,e){if(e&&ms(e.style)){var i;if(e._shadowDom)i=e._shadowDom,this.getDefs(!0).contains(e._shadowDom)||this.addDom(i);else i=this.add(e);this.markUsed(e);var r=i.getAttribute("id");t.style.filter="url(#"+r+")"}},_s.prototype.add=function(t){var e=this.createElement("filter");return t._shadowDomId=t._shadowDomId||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-shadow-"+t._shadowDomId),this.updateDom(t,e),this.addDom(e),e},_s.prototype.update=function(t,e){if(ms(e.style)){var i=this;ps.prototype.update.call(this,e,function(){i.updateDom(e,e._shadowDom)})}else this.remove(t,e)},_s.prototype.remove=function(t,e){null!=e._shadowDomId&&(this.removeDom(t),t.style.filter="")},_s.prototype.updateDom=function(t,e){var i=e.getElementsByTagName("feDropShadow");i=0===i.length?this.createElement("feDropShadow"):i[0];var r,n,a,o,s=t.style,h=t.scale&&t.scale[0]||1,l=t.scale&&t.scale[1]||1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)r=s.shadowOffsetX||0,n=s.shadowOffsetY||0,a=s.shadowBlur,o=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(e,s);r=s.textShadowOffsetX||0,n=s.textShadowOffsetY||0,a=s.textShadowBlur,o=s.textShadowColor}i.setAttribute("dx",r/h),i.setAttribute("dy",n/l),i.setAttribute("flood-color",o);var u=a/2/h+" "+a/2/l;i.setAttribute("stdDeviation",u),e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width",Math.ceil(a/2*200)+"%"),e.setAttribute("height",Math.ceil(a/2*200)+"%"),e.appendChild(i),t._shadowDom=e},_s.prototype.markUsed=function(t){t._shadowDom&&ps.prototype.markUsed.call(this,t._shadowDom)};function Cs(t,e,i,r){this.root=t,this.storage=e,this._opts=i=g({},i||{});var n=Ho("svg");n.setAttribute("xmlns","http://www.w3.org/2000/svg"),n.setAttribute("version","1.1"),n.setAttribute("baseProfile","full"),n.style.cssText="user-select:none;position:absolute;left:0;top:0;";var a=Ho("g");n.appendChild(a);var o=Ho("g");n.appendChild(o),this.gradientManager=new gs(r,o),this.clipPathManager=new vs(r,o),this.shadowManager=new _s(r,o);var s=document.createElement("div");s.style.cssText="overflow:hidden;position:relative",this._svgDom=n,this._svgRoot=o,this._backgroundRoot=a,this._viewport=s,t.appendChild(s),s.appendChild(n),this.resize(i.width,i.height),this._visibleList=[]}Cs.prototype={constructor:Cs,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getSvgDom:function(){return this._svgDom},getSvgRoot:function(){return this._svgRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0);this._paintList(t)},setBackgroundColor:function(t){this._backgroundRoot&&this._backgroundNode&&this._backgroundRoot.removeChild(this._backgroundNode);var e=Ho("rect");e.setAttribute("width",this.getWidth()),e.setAttribute("height",this.getHeight()),e.setAttribute("x",0),e.setAttribute("y",0),e.setAttribute("id",0),e.style.fill=t,this._backgroundRoot.appendChild(e),this._backgroundNode=e},_paintList:function(t){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();var e,i,r=this._svgRoot,n=this._visibleList,a=t.length,o=[];for(e=0;e<a;e++){var s=t[e],h=(i=s)instanceof _a?es:i instanceof Cr?is:i instanceof Ra?rs:es,l=Ss(s)||Ts(s);s.invisible||(s.__dirty&&(h&&h.brush(s),this.clipPathManager.update(s),s.style&&(this.gradientManager.update(s.style.fill),this.gradientManager.update(s.style.stroke),this.shadowManager.update(l,s)),s.__dirty=!1),o.push(s))}var u,c=function(t,e,i){return ds.diff(t,e,i)}(n,o);for(e=0;e<c.length;e++){if((p=c[e]).removed)for(var f=0;f<p.count;f++){l=Ss(s=n[p.indices[f]]);var d=Ts(s);ks(r,l),ks(r,d)}}for(e=0;e<c.length;e++){var p;if((p=c[e]).added)for(f=0;f<p.count;f++){l=Ss(s=o[p.indices[f]]),d=Ts(s);u?ws(r,l,u):bs(r,l),l?ws(r,d,l):u?ws(r,d,u):bs(r,d),ws(r,d,l),u=d||l||u,this.gradientManager.addWithoutUpdate(l||d,s),this.shadowManager.addWithoutUpdate(l||d,s),this.clipPathManager.markUsed(s)}else if(!p.removed)for(f=0;f<p.count;f++){l=Ss(s=o[p.indices[f]]),d=Ts(s),l=Ss(s),d=Ts(s);this.gradientManager.markUsed(s),this.gradientManager.addWithoutUpdate(l||d,s),this.shadowManager.markUsed(s),this.shadowManager.addWithoutUpdate(l||d,s),this.clipPathManager.markUsed(s),d&&ws(r,d,l),u=l||d||u}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=o},_getDefs:function(t){var r,e=this._svgDom;return 0!==(r=e.getElementsByTagName("defs")).length?r[0]:t?((r=e.insertBefore(Ho("defs"),e.firstChild)).contains||(r.contains=function(t){var e=r.children;if(!e)return!1;for(var i=e.length-1;0<=i;--i)if(e[i]===t)return!0;return!1}),r):null},resize:function(t,e){var i=this._viewport;i.style.display="none";var r=this._opts;if(null!=t&&(r.width=t),null!=e&&(r.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!==t||this._height!==e){this._width=t,this._height=e;var n=i.style;n.width=t+"px",n.height=e+"px";var a=this._svgDom;a.setAttribute("width",t),a.setAttribute("height",e)}this._backgroundNode&&(this._backgroundNode.setAttribute("width",t),this._backgroundNode.setAttribute("height",e))},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],r=["clientWidth","clientHeight"][t],n=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[r]||ys(s[i])||ys(o.style[i]))-(ys(s[n])||0)-(ys(s[a])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._backgroundRoot=this._svgDom=this._backgroundNode=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},toDataURL:function(){return this.refresh(),"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent(this._svgDom.outerHTML.replace(/></g,">\n\r<"))}},T(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","pathToImage"],function(t){Cs.prototype[t]=function(t){return function(){Ye('In SVG mode painter not support method "'+t+'"')}}(t)}),nn("svg",Cs);var Ms,As="urn:schemas-microsoft-com:vml",Ps="undefined"==typeof window?null:window,Ls=!1,zs=Ps&&Ps.document;function Ds(t){return Ms(t)}if(zs&&!_.canvasSupported)try{zs.namespaces.zrvml||zs.namespaces.add("zrvml",As),Ms=function(t){return zs.createElement("<zrvml:"+t+' class="zrvml">')}}catch(t){Ms=function(t){return zs.createElement("<"+t+' xmlns="'+As+'" class="zrvml">')}}var Bs,Is=Qn.CMD,Os=Math.round,Rs=Math.sqrt,Es=Math.abs,Fs=Math.cos,Hs=Math.sin,Ns=Math.max;if(!_.canvasSupported){var Ws=",",Vs="progid:DXImageTransform.Microsoft",Xs=21600,qs=Xs/2,js=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=Xs+","+Xs,t.coordorigin="0,0"},Ys=function(t,e,i){return"rgb("+[t,e,i].join(",")+")"},Us=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},Gs=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},Zs=function(t,e,i){return 1e5*(parseFloat(t)||0)+1e3*(parseFloat(e)||0)+i},Qs=xr,$s=function(t,e,i){var r=ye(e);i=+i,isNaN(i)&&(i=1),r&&(t.color=Ys(r[0],r[1],r[2]),t.opacity=i*r[3])},Ks=function(t,e,i,r){var n="fill"===e,a=t.getElementsByTagName(e)[0];null!=i[e]&&"none"!==i[e]&&(n||!n&&i.lineWidth)?(t[n?"filled":"stroked"]="true",i[e]instanceof Ua&&Gs(t,a),a=a||Ds(e),n?function(t,e,i){var r,n,a=e.fill;if(null!=a)if(a instanceof Ua){var o,s=0,h=[0,0],l=0,u=1,c=i.getBoundingRect(),f=c.width,d=c.height;if("linear"===a.type){o="gradient";var p=i.transform,g=[a.x*f,a.y*d],v=[a.x2*f,a.y2*d];p&&(nt(g,g,p),nt(v,v,p));var _=v[0]-g[0],m=v[1]-g[1];(s=180*Math.atan2(_,m)/Math.PI)<0&&(s+=360),s<1e-6&&(s=0)}else{o="gradientradial";g=[a.x*f,a.y*d],p=i.transform;var y=i.scale,x=f,w=d;h=[(g[0]-c.x)/x,(g[1]-c.y)/w],p&&nt(g,g,p),x/=y[0]*Xs,w/=y[1]*Xs;var b=Ns(x,w);l=0/b,u=2*a.r/b-l}var k=a.colorStops.slice();k.sort(function(t,e){return t.offset-e.offset});for(var T=k.length,S=[],C=[],M=0;M<T;M++){var A=k[M],P=(r=A.color,void 0,n=ye(r),[Ys(n[0],n[1],n[2]),n[3]]);C.push(A.offset*u+l+" "+P[0]),0!==M&&M!==T-1||S.push(P)}if(2<=T){var L=S[0][0],z=S[1][0],D=S[0][1]*e.opacity,B=S[1][1]*e.opacity;t.type=o,t.method="none",t.focus="100%",t.angle=s,t.color=L,t.color2=z,t.colors=C.join(","),t.opacity=B,t.opacity2=D}"radial"===o&&(t.focusposition=h.join(","))}else $s(t,a,e.opacity)}(a,i,r):function(t,e){e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof Ua||$s(t,e.stroke,e.opacity)}(a,i),Us(t,a)):(t[n?"filled":"stroked"]="false",Gs(t,a))},Js=[[],[],[]];_a.prototype.brushVML=function(t){var e=this.style,i=this._vmlEl;i||(i=Ds("shape"),js(i),this._vmlEl=i),Ks(i,"fill",e,this),Ks(i,"stroke",e,this);var r=this.transform,n=null!=r,a=i.getElementsByTagName("stroke")[0];if(a){var o=e.lineWidth;if(n&&!e.strokeNoScale){var s=r[0]*r[3]-r[1]*r[2];o*=Rs(Es(s))}a.weight=o+"px"}var h=this.path||(this.path=new Qn);this.__dirtyPath&&(h.beginPath(),h.subPixelOptimize=!1,this.buildPath(h,this.shape),h.toStatic(),this.__dirtyPath=!1),i.path=function(t,e){var i,r,n,a,o,s,h=Is.M,l=Is.C,u=Is.L,c=Is.A,f=Is.Q,d=[],p=t.data,g=t.len();for(a=0;a<g;){switch(r="",i=0,n=p[a++]){case h:r=" m ",i=1,o=p[a++],s=p[a++],Js[0][0]=o,Js[0][1]=s;break;case u:r=" l ",i=1,o=p[a++],s=p[a++],Js[0][0]=o,Js[0][1]=s;break;case f:case l:r=" c ",i=3;var v,_,m=p[a++],y=p[a++],x=p[a++],w=p[a++];n===f?(x=((v=x)+2*m)/3,w=((_=w)+2*y)/3,m=(o+2*m)/3,y=(s+2*y)/3):(v=p[a++],_=p[a++]),Js[0][0]=m,Js[0][1]=y,Js[1][0]=x,Js[1][1]=w,o=Js[2][0]=v,s=Js[2][1]=_;break;case c:var b=0,k=0,T=1,S=1,C=0;e&&(b=e[4],k=e[5],T=Rs(e[0]*e[0]+e[1]*e[1]),S=Rs(e[2]*e[2]+e[3]*e[3]),C=Math.atan2(-e[1]/S,e[0]/T));var M=p[a++],A=p[a++],P=p[a++],L=p[a++],z=p[a++]+C,D=p[a++]+z+C;a++;var B=p[a++],I=M+Fs(z)*P,O=A+Hs(z)*L,R=(m=M+Fs(D)*P,y=A+Hs(D)*L,B?" wa ":" at ");Math.abs(I-m)<1e-4&&(.01<Math.abs(D-z)?B&&(I+=.0125):Math.abs(O-A)<1e-4?B&&I<M||!B&&M<I?y-=.0125:y+=.0125:B&&O<A||!B&&A<O?m+=.0125:m-=.0125),d.push(R,Os(((M-P)*T+b)*Xs-qs),Ws,Os(((A-L)*S+k)*Xs-qs),Ws,Os(((M+P)*T+b)*Xs-qs),Ws,Os(((A+L)*S+k)*Xs-qs),Ws,Os((I*T+b)*Xs-qs),Ws,Os((O*S+k)*Xs-qs),Ws,Os((m*T+b)*Xs-qs),Ws,Os((y*S+k)*Xs-qs)),o=m,s=y;break;case Is.R:var E=Js[0],F=Js[1];E[0]=p[a++],E[1]=p[a++],F[0]=E[0]+p[a++],F[1]=E[1]+p[a++],e&&(nt(E,E,e),nt(F,F,e)),E[0]=Os(E[0]*Xs-qs),F[0]=Os(F[0]*Xs-qs),E[1]=Os(E[1]*Xs-qs),F[1]=Os(F[1]*Xs-qs),d.push(" m ",E[0],Ws,E[1]," l ",F[0],Ws,E[1]," l ",F[0],Ws,F[1]," l ",E[0],Ws,F[1]);break;case Is.Z:d.push(" x ")}if(0<i){d.push(r);for(var H=0;H<i;H++){var N=Js[H];e&&nt(N,N,e),d.push(Os(N[0]*Xs-qs),Ws,Os(N[1]*Xs-qs),H<i-1?Ws:"")}}}return d.join("")}(h,this.transform),i.style.zIndex=Zs(this.zlevel,this.z,this.z2),Us(t,i),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},_a.prototype.onRemove=function(t){Gs(t,this._vmlEl),this.removeRectText(t)},_a.prototype.onAdd=function(t){Us(t,this._vmlEl),this.appendRectText(t)};Cr.prototype.brushVML=function(t){var e,i,r=this.style,n=r.image;if(function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()}(n)){var a=n.src;if(a===this._imageSrc)e=this._imageWidth,i=this._imageHeight;else{var o=n.runtimeStyle,s=o.width,h=o.height;o.width="auto",o.height="auto",e=n.width,i=n.height,o.width=s,o.height=h,this._imageSrc=a,this._imageWidth=e,this._imageHeight=i}n=a}else n===this._imageSrc&&(e=this._imageWidth,i=this._imageHeight);if(n){var l=r.x||0,u=r.y||0,c=r.width,f=r.height,d=r.sWidth,p=r.sHeight,g=r.sx||0,v=r.sy||0,_=d&&p,m=this._vmlEl;m||(m=zs.createElement("div"),js(m),this._vmlEl=m);var y,x=m.style,w=!1,b=1,k=1;if(this.transform&&(y=this.transform,b=Rs(y[0]*y[0]+y[1]*y[1]),k=Rs(y[2]*y[2]+y[3]*y[3]),w=y[1]||y[2]),w){var T=[l,u],S=[l+c,u],C=[l,u+f],M=[l+c,u+f];nt(T,T,y),nt(S,S,y),nt(C,C,y),nt(M,M,y);var A=Ns(T[0],S[0],C[0],M[0]),P=Ns(T[1],S[1],C[1],M[1]),L=[];L.push("M11=",y[0]/b,Ws,"M12=",y[2]/k,Ws,"M21=",y[1]/b,Ws,"M22=",y[3]/k,Ws,"Dx=",Os(l*b+y[4]),Ws,"Dy=",Os(u*k+y[5])),x.padding="0 "+Os(A)+"px "+Os(P)+"px 0",x.filter=Vs+".Matrix("+L.join("")+", SizingMethod=clip)"}else y&&(l=l*b+y[4],u=u*k+y[5]),x.filter="",x.left=Os(l)+"px",x.top=Os(u)+"px";var z=this._imageEl,D=this._cropEl;z||(z=zs.createElement("div"),this._imageEl=z);var B=z.style;if(_){if(e&&i)B.width=Os(b*e*c/d)+"px",B.height=Os(k*i*f/p)+"px";else{var I=new Image,O=this;I.onload=function(){I.onload=null,e=I.width,i=I.height,B.width=Os(b*e*c/d)+"px",B.height=Os(k*i*f/p)+"px",O._imageWidth=e,O._imageHeight=i,O._imageSrc=n},I.src=n}D||((D=zs.createElement("div")).style.overflow="hidden",this._cropEl=D);var R=D.style;R.width=Os((c+g*c/d)*b),R.height=Os((f+v*f/p)*k),R.filter=Vs+".Matrix(Dx="+-g*c/d*b+",Dy="+-v*f/p*k+")",D.parentNode||m.appendChild(D),z.parentNode!==D&&D.appendChild(z)}else B.width=Os(b*c)+"px",B.height=Os(k*f)+"px",m.appendChild(z),D&&D.parentNode&&(m.removeChild(D),this._cropEl=null);var E="",F=r.opacity;F<1&&(E+=".Alpha(opacity="+Os(100*F)+") "),E+=Vs+".AlphaImageLoader(src="+n+", SizingMethod=scale)",B.filter=E,m.style.zIndex=Zs(this.zlevel,this.z,this.z2),Us(t,m),null!=r.text&&this.drawRectText(t,this.getBoundingRect())}},Cr.prototype.onRemove=function(t){Gs(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},Cr.prototype.onAdd=function(t){Us(t,this._vmlEl),this.appendRectText(t)};var th,eh="normal",ih={},rh=0,nh=document.createElement("div");Bs=function(t,e){var i=zs;th||((th=i.createElement("div")).style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",zs.body.appendChild(th));try{th.style.font=e}catch(t){}return th.innerHTML="",th.appendChild(i.createTextNode(t)),{width:th.offsetWidth}},Wi["measureText"]=Bs;for(var ah=new ri,oh=function(t,e,i,r){var n=this.style;this.__dirty&&hr(n);var a=n.text;if(null!=a&&(a+=""),a){if(n.rich){var o=Ji(a,n);a=[];for(var s=0;s<o.lines.length;s++){for(var h=o.lines[s].tokens,l=[],u=0;u<h.length;u++)l.push(h[u].text);a.push(l.join(""))}a=a.join("\n")}var c,f,d=n.textAlign,p=n.textVerticalAlign,g=function(t){var e=ih[t];if(!e){100<rh&&(rh=0,ih={});var i,r=nh.style;try{r.font=t,i=r.fontFamily.split(",")[0]}catch(t){}e={style:r.fontStyle||eh,variant:r.fontVariant||eh,weight:r.fontWeight||eh,size:0|parseFloat(r.fontSize||12),family:i||"Microsoft YaHei"},ih[t]=e,rh++}return e}(n.font),v=g.style+" "+g.variant+" "+g.weight+" "+g.size+'px "'+g.family+'"';i=i||Xi(a,v,d,p,n.textPadding,n.textLineHeight);var _=this.transform;if(_&&!r&&(ah.copy(e),ah.applyTransform(_),e=ah),r)c=e.x,f=e.y;else{var m=n.textPosition;if(m instanceof Array)c=e.x+Qs(m[0],e.width),f=e.y+Qs(m[1],e.height),d=d||"left";else{var y=this.calculateTextPosition?this.calculateTextPosition({},n,e):Yi({},n,e);c=y.x,f=y.y,d=d||y.textAlign,p=p||y.textVerticalAlign}}c=qi(c,i.width,d),f=ji(f,i.height,p),f+=i.height/2;var x,w,b,k=Ds,T=this._textVmlEl;T?w=(x=(b=T.firstChild).nextSibling).nextSibling:(T=k("line"),x=k("path"),w=k("textpath"),b=k("skew"),w.style["v-text-align"]="left",js(T),x.textpathok=!0,w.on=!0,T.from="0 0",T.to="1000 0.05",Us(T,b),Us(T,x),Us(T,w),this._textVmlEl=T);var S=[c,f],C=T.style;_&&r?(nt(S,S,_),b.on=!0,b.matrix=_[0].toFixed(3)+Ws+_[2].toFixed(3)+Ws+_[1].toFixed(3)+Ws+_[3].toFixed(3)+",0,0",b.offset=(Os(S[0])||0)+","+(Os(S[1])||0),b.origin="0 0",C.left="0px",C.top="0px"):(b.on=!1,C.left=Os(c)+"px",C.top=Os(f)+"px"),w.string=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")}(a);try{w.style.font=v}catch(t){}Ks(T,"fill",{fill:n.textFill,opacity:n.opacity},this),Ks(T,"stroke",{stroke:n.textStroke,opacity:n.opacity,lineDash:n.lineDash||null},this),T.style.zIndex=Zs(this.zlevel,this.z,this.z2),Us(t,T)}},sh=function(t){Gs(t,this._textVmlEl),this._textVmlEl=null},hh=function(t){Us(t,this._textVmlEl)},lh=[kr,Sr,Cr,_a,Ra],uh=0;uh<lh.length;uh++){var ch=lh[uh].prototype;ch.drawRectText=oh,ch.removeRectText=sh,ch.appendRectText=hh}Ra.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},Ra.prototype.onRemove=function(t){this.removeRectText(t)},Ra.prototype.onAdd=function(t){this.appendRectText(t)}}function fh(t){return parseInt(t,10)}function dh(t,e){!function(){if(!Ls&&zs){Ls=!0;var t=zs.styleSheets;t.length<31?zs.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}(),this.root=t,this.storage=e;var i=document.createElement("div"),r=document.createElement("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",r.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=r,this._vmlViewport=i,this.resize();var n=e.delFromStorage,a=e.addToStorage;e.delFromStorage=function(t){n.call(e,t),t&&t.onRemove&&t.onRemove(r)},e.addToStorage=function(t){t.onAdd&&t.onAdd(r),a.call(e,t)},this._firstPaint=!0}dh.prototype={constructor:dh,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,i=0;i<t.length;i++){var r=t[i];r.invisible||r.ignore?(r.__alreadyNotVisible||r.onRemove(e),r.__alreadyNotVisible=!0):(r.__alreadyNotVisible&&r.onAdd(e),r.__alreadyNotVisible=!1,r.__dirty&&(r.beforeBrush&&r.beforeBrush(),(r.brushVML||r.brush).call(r,e),r.afterBrush&&r.afterBrush())),r.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!==t||this._height!==e){this._width=t,this._height=e;var i=this._vmlViewport.style;i.width=t+"px",i.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||fh(e.width))-fh(e.paddingLeft)-fh(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||fh(e.height))-fh(e.paddingTop)-fh(e.paddingBottom)|0}},T(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){dh.prototype[t]=function(t){return function(){Ye('In IE8.0 VML mode painter not support method "'+t+'"')}}(t)}),nn("vml",dh),t.version="4.3.2",t.init=function(t,e){var i=new an(r(),t,e);return rn[i.id]=i},t.dispose=function(t){if(t)t.dispose();else{for(var e in rn)rn.hasOwnProperty(e)&&rn[e].dispose();rn={}}return this},t.getInstance=function(t){return rn[t]},t.registerPainter=nn,t.matrix=Yt,t.vector=st,t.color=Me,t.path=Oa,t.util=V,t.parseSVG=function(t,e){return(new Ka).parse(t,e)},t.Group=ni,t.Path=_a,t.Image=Cr,t.CompoundPath=lo,t.Text=Ra,t.IncrementalDisplayable=uo,t.Arc=fo,t.BezierCurve=mo,t.Circle=Ea,t.Droplet=yo,t.Ellipse=Va,t.Heart=xo,t.Isogon=To,t.Line=qa,t.Polygon=Ga,t.Polyline=Za,t.Rect=Wa,t.Ring=So,t.Rose=Po,t.Sector=zo,t.Star=Oo,t.Trochoid=Fo,t.LinearGradient=Qa,t.RadialGradient=vo,t.Pattern=Ci,t.BoundingRect=ri});
