<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.MendianDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.mendian_name as mendianName
        ,a.mendian_types as mendianTypes
        ,a.mendian_photo as mendianPhoto
        ,a.insert_time as insertTime
        ,a.mendian_content as mendianContent
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.MendianView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段

        FROM mendian  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.mendianName != '' and params.mendianName != null and params.mendianName != 'null' ">
                and a.mendian_name like CONCAT('%',#{params.mendianName},'%')
            </if>
            <if test="params.mendianTypes != null and params.mendianTypes != ''">
                and a.mendian_types = #{params.mendianTypes}
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test=" params.mendianContent != '' and params.mendianContent != null and params.mendianContent != 'null' ">
                and a.mendian_content like CONCAT('%',#{params.mendianContent},'%')
            </if>

        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>