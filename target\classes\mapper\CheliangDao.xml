<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.CheliangDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.cheliang_name as cheliangName
        ,a.cheliang_types as cheliangTypes
        ,a.cheliang_photo as cheliangPhoto
        ,a.yonghu_id as yonghuId
        ,a.insert_time as insertTime
        ,a.cheliang_content as cheliangContent
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.CheliangView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段
        ,yonghu.yonghu_name as yonghu<PERSON><PERSON>
        ,yonghu.yonghu_photo as yonghu<PERSON>hot<PERSON>
        ,yonghu.yonghu_phone as yonghu<PERSON><PERSON>
        ,yonghu.yonghu_email as yonghuEmail
        ,yonghu.new_money as newMoney
        ,yonghu.yonghu_delete as yonghuDelete

        FROM cheliang  a
        left JOIN yonghu yonghu ON a.yonghu_id = yonghu.id

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.cheliangName != '' and params.cheliangName != null and params.cheliangName != 'null' ">
                and a.cheliang_name like CONCAT('%',#{params.cheliangName},'%')
            </if>
            <if test="params.cheliangTypes != null and params.cheliangTypes != ''">
                and a.cheliang_types = #{params.cheliangTypes}
            </if>
            <if test="params.yonghuId != null and params.yonghuId != ''">
                and (
                    a.yonghu_id = #{params.yonghuId}
                )
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test=" params.cheliangContent != '' and params.cheliangContent != null and params.cheliangContent != 'null' ">
                and a.cheliang_content like CONCAT('%',#{params.cheliangContent},'%')
            </if>

                <!-- 判断用户的id不为空 -->
            <if test=" params.yonghuIdNotNull != '' and params.yonghuIdNotNull != null and params.yonghuIdNotNull != 'null' ">
                and a.yonghu_id IS NOT NULL
            </if>
            <if test=" params.yonghuName != '' and params.yonghuName != null and params.yonghuName != 'null' ">
                and yonghu.yonghu_name like CONCAT('%',#{params.yonghuName},'%')
            </if>
            <if test=" params.yonghuPhone != '' and params.yonghuPhone != null and params.yonghuPhone != 'null' ">
                and yonghu.yonghu_phone like CONCAT('%',#{params.yonghuPhone},'%')
            </if>
            <if test=" params.yonghuEmail != '' and params.yonghuEmail != null and params.yonghuEmail != 'null' ">
                and yonghu.yonghu_email like CONCAT('%',#{params.yonghuEmail},'%')
            </if>
            <if test="params.newMoneyStart != null ">
                <![CDATA[  and yonghu.new_money >= #{params.newMoneyStart}   ]]>
            </if>
            <if test="params.newMoneyEnd != null ">
                <![CDATA[  and yonghu.new_money <= #{params.newMoneyEnd}   ]]>
            </if>
            <if test="params.yonghuDeleteStart != null  and params.yonghuDeleteStart != '' ">
                <![CDATA[  and yonghu.yonghu_delete >= #{params.yonghuDeleteStart}   ]]>
            </if>
            <if test="params.yonghuDeleteEnd != null  and params.yonghuDeleteEnd != '' ">
                <![CDATA[  and yonghu.yonghu_delete <= #{params.yonghuDeleteEnd}   ]]>
            </if>
            <if test="params.yonghuDelete != null  and params.yonghuDelete != '' ">
                and yonghu.yonghu_delete = #{params.yonghuDelete}
            </if>
        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>